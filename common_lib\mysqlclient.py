import pymysql
from loguru import logger


class MySqlClient(object):
    """
    操作mysql数据库
    """
    def __init__(self, config: dict):
        """
        初始化函数，用于设置数据库连接的相关配置。

        Args:
            config (dict): 包含数据库连接配置的字典。字典中应包含以下键：
                - "hosts": 数据库主机名或IP地址，默认为空字符串。
                - "user": 数据库用户名，默认为"root"。
                - "password": 数据库用户密码，默认为"root"。
                - "db": 数据库名称，默认为"jeecg-boot"。
        """
        # 从配置字典中获取数据库连接参数，并设置默认值
        self.host_name = config.get("hosts", "")
        self.user_name = config.get("user", "root")
        self.user_passwd = config.get("password", "root")
        self.db_name = config.get("db", "jeecg-boot")
        
        # 初始化数据库连接和游标为None
        self.conn = None
        self.cur = None
        
        # 调用connect方法建立数据库连接
        self.connect()


    def __del__(self) -> None:
        self.close()

    @logger.catch
    def connect(self):
        """
        与MySQL数据库建立连接并创建游标。

        该方法尝试使用给定的主机名、用户名、密码和数据库名称连接到MySQL数据库。
        如果连接成功，将创建数据库游标以供后续操作使用。
        如果连接失败，将记录错误信息，并将连接和游标设置为None。

        异常处理：
            如果在连接过程中发生任何异常，将捕获异常并记录错误信息，
            同时将连接和游标设置为None。

        返回值：
            无返回值。
        """
        try:
            logger.debug("程序与mysql数据库建立连接.")
            # 使用给定的主机名、用户名、密码和数据库名称连接到MySQL数据库
            self.conn = pymysql.connect(host=self.host_name, user=self.user_name,
                                    password=self.user_passwd, database=self.db_name, charset='utf8')
            # 创建数据库游标，用于执行SQL语句
            self.cur = self.conn.cursor()
        except Exception as err:
            logger.error(f'连接失败: {err}')
            # 如果连接失败，将连接和游标设置为None
            self.conn = None
            self.cur = None

    @logger.catch
    def query(self, sql_str):
        """
        执行SQL查询并返回结果。

        参数:
        - sql_str (str): 要执行的SQL查询语句。

        返回值:
        - list: 查询结果的所有记录，以列表形式返回。如果执行失败或参数无效，则返回None。
        """
        try:
            # 检查数据库连接状态
            self.check()
            
            # 如果游标或SQL语句为空，则返回None
            if self.cur is None or sql_str is None:
                return None
            
            # 执行SQL查询
            self.cur.execute(sql_str)
            
            # 获取所有查询结果
            return self.cur.fetchall()
        except Exception as err:
            # 记录错误日志，并返回None
            logger.error("MYSQL Error %s for execute %s " % (err, sql_str))
            return None

    def close(self):
        """
        关闭数据库连接和游标，释放相关资源。

        该方法用于关闭与数据库的连接和游标。如果游标和连接对象存在，则分别调用它们的 `close()` 方法，
        以确保释放与数据库相关的资源，避免内存泄漏。

        参数:
            无

        返回值:
            无
        """
        if self.cur is not None:
            # 关闭游标，释放游标占用的资源
            self.cur.close()
        if self.conn is not None:
            # 关闭数据库连接，释放连接占用的资源
            self.conn.close()

    @logger.catch
    def check(self):
        """
        检查当前连接状态，并在连接异常时尝试重新连接。

        该方法通过调用连接对象的 `ping()` 函数来检测连接状态。如果检测过程中出现异常，
        则会记录错误日志并尝试重新建立连接。

        参数:
            无

        返回值:
            无
        """
        try:
            # 检测连接状态
            self.conn.ping()
        except Exception as err:
            # 记录异常并尝试重新连接
            logger.error(f'出现异常重新连接. {err}')
            self.connect()

    @logger.catch
    def insert_query(self, table:str, values:str, columns:str='')->bool:
        """
        在数据库指定表中新增一条数据

        :param table: 表名，类型为字符串，表示要插入数据的表
        :param values: 需要插入的值，类型为字符串，表示要插入的具体值
        :param columns: 需要插入的列，类型为字符串，默认为空串，表示插入所有列
        :return: 插入成功返回True，失败返回False
        """
        # 检查参数类型是否正确，如果参数类型错误则记录错误日志并返回False
        if not isinstance(table, str)  or not isinstance(values, str):
            logger.error("Invalid parameters: table=%s, values=%s" % (table, values))
            return False
        
        try:
            # 检查数据库连接是否有效
            self.check()
            if self.cur is None:
                return False
            
            # 构建SQL插入语句，将表名、列名和值拼接成完整的SQL语句
            sql_str = f"INSERT INTO {table} {columns} VALUES {values};"
            
            # 执行SQL插入语句
            self.cur.execute(sql_str)
            
            # 提交事务，确保数据插入操作生效
            self.conn.commit()
            logger.debug("Data inserted successfully: %s" % sql_str)
            return True
        except Exception as err:
            # 如果发生异常，回滚事务并记录错误日志
            self.conn.rollback()
            logger.error("MYSQL Error %s for execute %s" % (err, sql_str))
            return False

    @logger.catch
    def delete_query(self, table:str, where_str:str)->bool:
        """
        在数据库指定表中删除一条数据

        :param table: 表名，类型为字符串，表示要删除数据的表
        :param where_str: 删除条件，类型为字符串，表示删除数据的条件
        :return: 删除成功返回True，失败返回False
        """
        # 检查参数类型，确保table和where_str为字符串类型
        if not isinstance(table, str) or not isinstance(where_str, str):
            logger.error("Invalid parameters: table=%s, where_str=%s" % (table, where_str))
            return False
        
        try:
            # 检查数据库连接状态
            self.check()
            if self.cur is None:
                return False
            
            # 构建SQL删除语句
            sql_str = f"DELETE FROM {table} WHERE {where_str};"
            
            # 执行删除操作
            self.cur.execute(sql_str)
            
            # 提交事务，确保删除操作生效
            self.conn.commit()
            logger.debug("Data deleted successfully: %s" % sql_str)
            return True
        except Exception as err:
            # 发生错误时回滚事务，确保数据一致性
            self.conn.rollback()
            logger.error("MYSQL Error %s for execute %s" % (err, sql_str))
            return False

    @logger.catch
    def update_query(self, table:str, set_str:str, where_str:str):
        """
        修改数据库指定表中的数据

        :param table: 表名，类型为字符串，表示要更新的数据库表名称。
        :param set_str: 新的数据信息，类型为字符串，格式为 "column_name1=data1, column_name2=data2..."，表示要更新的列及其对应的新值。
        :param where_str: 查询条件，类型为字符串，表示更新数据的条件。
        :return: 如果更新成功返回True，如果失败返回False。
        """        
        # 检查参数类型是否正确，如果参数类型不正确，记录错误日志并返回False
        if not isinstance(table, str) or not isinstance(set_str, str) or not isinstance(where_str, str):
            logger.error("Invalid parameters: table=%s, set_str=%s, where_str=%s" % (table, set_str, where_str))
            return False
        
        try:
            # 检查数据库连接和游标是否有效
            self.check()
            if self.cur is None:
                return False
            
            # 构造SQL更新语句并执行
            sql_str = f"UPDATE {table} SET {set_str} WHERE {where_str};"
            self.cur.execute(sql_str)
            
            # 提交事务，确保更新操作生效
            self.conn.commit()
            logger.debug("Data updated successfully: %s" % sql_str)
            return True
        except Exception as err:
            # 发生错误时回滚事务，并记录错误日志
            self.conn.rollback()
            logger.error("MYSQL Error %s for execute %s" % (err, sql_str))
            return False

    @logger.catch
    def select_query(self, table:str, columns:str="*", where_str:str=None):
        """
        查询数据库指定表中的数据

        :param table: 表名，指定要查询的数据库表
        :param columns: 需要查询的列，默认为所有列（即使用*）
        :param where_str: 查询条件，默认为None，表示无条件查询
        :return: 查询结果，返回查询到的所有记录；如果查询失败，返回None
        """
        # 检查输入参数的类型是否正确
        if not isinstance(table, str) or not isinstance(columns, str) or (where_str is not None and not isinstance(where_str, str)):
            logger.error("Invalid parameters: table=%s, columns=%s, where_str=%s" % (table, columns, where_str))
            return None
        
        try:
            # 检查数据库连接是否正常
            self.check()
            if self.cur is None:
                return None
            
            # 构建SQL查询语句
            sql_str = f"SELECT {columns} FROM {table}"
            if where_str:
                sql_str += f" WHERE {where_str};"
            else:
                sql_str += ";"
            
            # 执行查询并获取结果
            self.cur.execute(sql_str)
            result = self.cur.fetchall()
            logger.debug("Data queried successfully: %s" % sql_str)
            return result
        except Exception as err:
            # 捕获并记录查询过程中发生的异常
            logger.error("MYSQL Error %s for execute %s" % (err, sql_str))
            return None

    @logger.catch
    def get_table_columns(self, table_name: str):
        """
        查询指定表格的列名

        :param table_name: 表名，类型为字符串
        :return: 列名列表，如果失败返回 None
        """
        # 检查参数类型，确保 table_name 是字符串类型
        if not isinstance(table_name, str):
            logger.error("Invalid parameters: table_name=%s" % table_name)
            return None

        try:
            # 检查数据库连接状态
            self.check()
            if self.cur is None:
                return None

            # 构建SQL查询语句，查询指定表的列名
            sql_str = f"SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = '{self.db_name}' AND TABLE_NAME = '{table_name}';"
            
            # 执行SQL查询
            self.cur.execute(sql_str)
            
            # 从查询结果中提取所有列名
            columns = [row[0] for row in self.cur.fetchall()]
            logger.debug("Columns queried successfully: %s" % sql_str)
            return columns
        except Exception as err:
            # 捕获并记录SQL执行过程中的异常
            logger.error("MYSQL Error %s for execute %s" % (err, sql_str))
            return None

    @logger.catch
    def paginate_query(self, table_name: str, page: int, page_size: int):
        """
        按页查询指定表格的内容

        :param table_name: 表名，字符串类型，表示要查询的数据库表名称
        :param page: 页码，整数类型，表示要查询的页码（从1开始）
        :param page_size: 每页记录数，整数类型，表示每页要查询的记录数量
        :return: 查询结果，返回查询到的记录列表。如果查询失败或参数无效，返回 None
        """
        # 检查参数类型是否有效
        if not isinstance(table_name, str) or not isinstance(page, int) or not isinstance(page_size, int):
            logger.error("Invalid parameters: table_name=%s, page=%s, page_size=%s" % (table_name, page, page_size))
            return None

        try:
            # 检查数据库连接状态
            self.check()
            if self.cur is None:
                return None

            # 计算查询的偏移量
            offset = (page - 1) * page_size

            # 构建SQL查询语句，使用LIMIT和OFFSET实现分页
            sql_str = f"SELECT * FROM {table_name} LIMIT {page_size} OFFSET {offset};"
            
            # 执行SQL查询
            self.cur.execute(sql_str)
            
            # 获取查询结果
            result = self.cur.fetchall()
            logger.debug("Data paginated successfully: %s" % sql_str)
            return result
        except Exception as err:
            # 捕获并记录SQL执行过程中的异常
            logger.error("MYSQL Error %s for execute %s" % (err, sql_str))
            return None
