# -*- coding: utf-8 -*-
""" 公共函数库
python 版本：python3
Name：tools.py
Description:一个公共的函数库
# Author:胡旭
# Version:2.0
# Date:2025/3/07
"""
import json
import time
import datetime
import pytz
from loguru import logger

def init_log(name:str = "cport"):
    """
    初始化日志配置，创建两个日志文件：一个用于记录DEBUG级别日志，另一个用于记录ERROR级别日志。
    
    参数:
    name (str): 日志文件的前缀名称，默认为"cport"。
    
    返回值:
    无返回值。
    """
    # 日志文件存储路径
    path = "log"
    
    # 添加DEBUG级别日志配置
    # 日志文件按5MB大小进行轮转，保留7天，压缩为zip格式，并启用队列
    logger.add(
        sink=f"{path}/{name}_debug_{{time}}.log",
        level="DEBUG",
        rotation="5 MB",
        retention="7 days",
        compression="zip",
        enqueue=True
    )
    
    # 添加ERROR级别日志配置
    # 日志文件按5MB大小进行轮转，保留7天，压缩为zip格式，并启用队列
    logger.add(
        sink=f"{path}/{name}_err_{{time}}.log",
        level="ERROR",
        rotation="5 MB",
        retention="7 days",
        compression="zip",
        enqueue=True
    )


def write_json(filename:str, data:any)->bool:
    """ 将数据写入json文件中

    Args:
        filename (str): json文件名称，包含文件路径和扩展名
        data (any): 准备写入文件的数据，可以是字典、列表等Python数据结构

    Returns:
        bool: 如果数据成功写入文件，返回True；如果写入过程中发生IOError，返回False
    """
    try:
        # 打开文件并以UTF-8编码写入数据
        with open(filename, "w", encoding="utf-8") as fileio:
            # 将数据以JSON格式写入文件，确保非ASCII字符正常显示，并设置缩进为2个空格
            json.dump(data, fileio, ensure_ascii=False, indent=2, skipkeys=True)
        return True
    except IOError:
        # 如果文件写入失败，记录错误日志并返回False
        logger.error(f"\n文件<{filename}>写数据失败！")
        return False


def read_json(filename:str) -> any:
    """读取json文件中数据

    Args:
        filename (str): json文件名称，包含文件路径和扩展名。

    Returns:
        any: json反序列化后得到的数据，如果文件读取失败则返回None。

    Raises:
        IOError: 如果文件无法打开或读取，将记录错误日志并返回None。
    """
    try:
        # 打开文件并读取json数据
        with open(filename, "r", encoding="utf-8") as fileio:
            msg = json.load(fileio)
            return msg
    except IOError:
        # 文件读取失败时记录错误日志
        logger.error(f"\n文件<{filename}>读取失败！")
        return None


def write_dat(filename:str, data:bytes)->bool:
    """将bytes数据流写入dat文件中

    Args:
        filename (str): dat文件名称，指定要写入的文件路径及名称
        data (bytes): 二进制数据流，包含要写入文件的二进制数据

    Returns:
        bool: 写文件是否成功，成功返回True，失败返回False
    """
    try:
        # 以二进制写模式打开文件，并将数据写入文件
        with open(filename, "wb") as fileio:
            fileio.write(data)
            return True
    except IOError:
        # 捕获IOError异常，记录错误日志并返回False
        logger.error(f"\n文件<{filename}>写数据失败！")
        return False


def read_dat(filename: str) -> bytes:
    """ 读取dat文件中数据

    Args:
        filename (str): dat文件名称，包含文件路径和扩展名。

    Returns:
        bytes: 读取到的二进制数据流。如果读取失败，返回None。

    Raises:
        IOError: 如果文件读取过程中发生错误，会捕获并记录错误日志。
    """
    try:
        # 以二进制模式打开文件并读取数据
        with open(filename, "rb") as fileio:
            data = fileio.read()
            return data
    except IOError:
        # 如果文件读取失败，记录错误日志并返回None
        logger.error(f"\n文件<{filename}>读取失败！")
        return None


def count_time(func: callable):
    """ 计算函数耗时的装饰器函数
        输出耗时大于10ms的函数名称及耗时时间

    Args:
        func (callable): 被装饰的函数，可以是任何可调用对象。

    Returns:
        wrapper: 返回一个内部函数，该函数用于计算被装饰函数的执行时间。
    """
    def wrapper(*args, **kwargs):
        # 记录函数开始执行的时间
        time1 = time.time()
        
        # 执行被装饰的函数，并获取其返回值
        ret = func(*args, **kwargs)
        
        # 记录函数执行结束的时间
        time2 = time.time()
        
        # 计算函数执行的总耗时
        all_time = time2 - time1
        
        # 如果耗时大于10ms，则输出函数名称及耗时时间
        if all_time > 0.01:
            print(f"{func.__name__}执行时间为：{all_time} s")
        
        # 返回被装饰函数的返回值
        return ret
    
    # 返回内部函数wrapper，用于替换被装饰的函数
    return wrapper

def get_timestamp_ms(time_str = None)-> int:
    """计算输入时间字符串的13位时间戳

    Args:
        time_str (str, optional): 时间字符串，格式为'%Y-%m-%d %H:%M:%S'。如果未提供，则返回当前时间的13位时间戳。默认为None。

    Returns:
        int: 13位时间戳，单位为毫秒。
    """
    if time_str is None:
        # 如果未提供时间字符串，返回当前时间的13位时间戳
        return int(time.time() * 1000)
    
    # 初始化毫秒部分为0
    ms = 0
    
    # 如果时间字符串包含小数点，分割字符串以获取毫秒部分
    if time_str.find('.') > 0:
         names = time_str.split('.')
         time_str = names[0]
         ms = int(names[1])
    
    # 将时间字符串解析为时间元组
    struct_time = time.strptime(time_str, "%Y-%m-%d %H:%M:%S")
    
    # 将时间元组转换为时间戳，并加上毫秒部分，返回13位时间戳
    timestamp = (time.mktime(struct_time) * 1000) + ms 
    return int(timestamp)
def get_timestamp(time_str = None)-> int:
    """计算输入时间字符串的10位时间戳

    Args:
        time_str (str, optional): 时间字符串，格式为"%Y-%m-%d %H:%M:%S"。如果未提供，则返回当前时间的时间戳。默认为None。

    Returns:
        int: 10位时间戳，表示从1970年1月1日00:00:00 UTC到指定时间的秒数。
    """
    if time_str is None:
        # 如果未提供时间字符串，返回当前时间的时间戳
        return int(time.time())
    
    # 如果时间字符串包含毫秒部分，去除毫秒部分
    if time_str.find('.') > 0:
         time_str = time_str.split('.')[0]
    
    # 将时间字符串转换为时间结构体
    struct_time = time.strptime(time_str, "%Y-%m-%d %H:%M:%S")
    
    # 将时间结构体转换为时间戳
    timestamp = time.mktime(struct_time)
    
    # 返回10位时间戳
    return int(timestamp)

def get_curtime(timestamp: int = None) -> str:
    """获取当前时间的字符串

    根据传入的时间戳或当前时间生成ISO格式的时间字符串，并对其进行格式化处理。

    Args:
        timestamp (int, optional): 时间戳，单位为毫秒。如果未提供，则使用当前时间。Defaults to None.

    Returns:
        str: 格式化后的时间字符串，格式为 'YYYY-MM-DD HH:MM:SS.sss'。如果时间戳无效，则返回None。
    """
    if timestamp is None:
        # 获取当前时间的ISO格式字符串
        unix_ts = datetime.datetime.now().isoformat()
    elif isinstance(timestamp, int):
        # 将时间戳转换为ISO格式字符串，时间戳单位为毫秒
        unix_ts = datetime.datetime.fromtimestamp(timestamp / 1000).isoformat()
    else:
        # 如果时间戳无效，返回None
        return None

    # 处理ISO格式字符串，确保时间部分包含毫秒
    if unix_ts[-3] == ':':
        # 如果时间部分不包含毫秒，则添加 '.000'
        return f'{unix_ts[:10]} {unix_ts[11:]}.000'
    # 返回格式化后的时间字符串
    return f'{unix_ts[:10]} {unix_ts[11:-3]}'

def get_elapse_time(timestamp: int) -> int:
    """获取当前距输入时间过去的时间，单位s

    Args:
        timestamp (int): 时间戳，兼容13位ms、10位s。如果输入的时间戳不是整数类型，函数将返回一个默认值。

    Returns:
        int: 返回当前时间与输入时间戳之间的时间差，单位为秒。如果输入的时间戳无效，返回默认值1723132800。
    """
    # 2024-08-09 00:00:00 = 1723132800000
    if isinstance(timestamp, int) is False:
        # 如果输入的时间戳不是整数类型，返回默认值1723132800
        return 1723132800

    now = time.time()

    # 根据时间戳的长度判断其单位是毫秒还是秒，并计算时间差
    if timestamp > 1723132800000:       # 单位：ms
        return abs(now - (timestamp / 1000))
    elif timestamp > 1723132800:
        return abs(now - timestamp)     # 单位：s

    # 如果时间戳小于等于默认值，返回默认值1723132800
    return 1723132800

def getcurtimestamp()-> int:
    """获取当前的时间戳值，单位ms

    Returns:
        int: 13位时间戳数值，单位ms
    """
    # 获取当前时间
    now = datetime.datetime.now()
    
    # 将当前时间转换为时间戳，并乘以1000以转换为毫秒
    return int(now.timestamp() * 1000)

def convert_time_format(original_time_str, target_timezone_str='Asia/Shanghai'):
    """
    将ISO 8601格式的时间字符串转换为指定时区的时间字符串。

    :param original_time_str: 原始时间字符串，格式为 '2025-03-04T08:55:33+00:00'，表示ISO 8601格式的时间。
    :param target_timezone_str: 目标时区字符串，默认为 'Asia/Shanghai'，表示要转换到的时区。
    :return: 转换后的时间字符串，格式为 '2025-03-04 10:53:23'，表示目标时区的时间。
    """
    # 解析原始时间字符串为datetime对象
    original_time = datetime.datetime.fromisoformat(original_time_str)
    
    # 获取目标时区的pytz对象
    target_timezone = pytz.timezone(target_timezone_str)
    
    # 如果原始时间没有时区信息，假设它是UTC时间，并为其添加UTC时区信息
    if original_time.tzinfo is None:
        original_time = pytz.utc.localize(original_time)
    
    # 将时间转换为目标时区
    target_time = original_time.astimezone(target_timezone)
    
    # 将时间格式化为目标格式的字符串
    formatted_time_str = target_time.strftime('%Y-%m-%d %H:%M:%S')
    
    return formatted_time_str

