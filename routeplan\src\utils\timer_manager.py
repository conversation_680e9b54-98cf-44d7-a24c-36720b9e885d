"""
定时器管理器模块
负责异步管理定时器的创建、执行和取消
"""

from typing import Optional, Callable, Dict, Any
import threading
import time
from concurrent.futures import ThreadPoolExecutor
from ..utils.logging import get_logger

logger = get_logger(__name__)


class TimerManager:
    """定时器管理器，负责异步管理定时器"""

    # 单例模式
    _instance = None
    _lock = threading.Lock()

    # 定时器字典，存储所有定时器
    _timers = {}

    # 线程池
    _executor = None

    # 关闭状态标志
    is_shutting_down = False

    def __new__(cls):
        """单例模式实现"""
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(TimerManager, cls).__new__(cls)
                # 初始化线程池 - 进一步限制线程数量，降低内存使用
                cls._instance._executor = ThreadPoolExecutor(
                    max_workers=2,  # 进一步减少最大工作线程数
                    thread_name_prefix="TimerManager",  # 添加线程名称前缀，便于调试
                )
                # 初始化定时器字典
                cls._instance._timers = {}
            return cls._instance

    def start_timer(
        self, timer_name: str, interval: int, callback: Callable, *args, **kwargs
    ) -> bool:
        """
        启动定时器

        Args:
            timer_name: 定时器名称
            interval: 定时器间隔（秒）
            callback: 回调函数
            *args: 回调函数的位置参数
            **kwargs: 回调函数的关键字参数

        Returns:
            bool: 是否成功启动定时器
        """
        # 如果定时器已存在，先取消
        if timer_name in self._timers:
            self.cancel_timer(timer_name)

        # 创建定时器
        try:
            # 创建定时器对象
            timer = threading.Timer(
                interval,
                self._timer_callback,
                args=(timer_name, interval, callback, args, kwargs),
            )
            timer.daemon = True  # 设为守护线程

            # 存储定时器信息
            self._timers[timer_name] = {
                "timer": timer,
                "interval": interval,
                "callback": callback,
                "args": args,
                "kwargs": kwargs,
                "last_run": None,
                "next_run": time.time() + interval,
            }

            # 启动定时器
            timer.start()
            logger.debug(f"定时器 {timer_name} 已启动，间隔: {interval}秒")
            return True

        except Exception as e:
            logger.error(f"启动定时器 {timer_name} 失败: {str(e)}")
            return False

    def _timer_callback(
        self,
        timer_name: str,
        interval: int,
        callback: Callable,
        args: tuple,
        kwargs: dict,
    ) -> None:
        """
        定时器回调函数

        Args:
            timer_name: 定时器名称
            interval: 定时器间隔（秒）
            callback: 回调函数
            args: 回调函数的位置参数
            kwargs: 回调函数的关键字参数
        """
        # 如果正在关闭，不执行回调
        if self.is_shutting_down:
            return

        try:
            # 记录执行开始时间
            start_time = time.time()

            # 更新定时器信息
            if timer_name in self._timers:
                self._timers[timer_name]["last_run"] = start_time

            # 异步执行回调函数
            self._executor.submit(callback, *args, **kwargs)

            # 如果定时器仍然存在，重新启动
            if timer_name in self._timers and not self.is_shutting_down:
                # 创建新的定时器
                new_timer = threading.Timer(
                    interval,
                    self._timer_callback,
                    args=(timer_name, interval, callback, args, kwargs),
                )
                new_timer.daemon = True

                # 更新定时器信息
                self._timers[timer_name]["timer"] = new_timer
                self._timers[timer_name]["next_run"] = time.time() + interval

                # 启动新的定时器
                new_timer.start()

        except Exception as e:
            logger.error(f"执行定时器 {timer_name} 回调函数时出错: {str(e)}")

            # 如果出错但定时器仍然存在，尝试重新启动
            if timer_name in self._timers and not self.is_shutting_down:
                try:
                    # 创建新的定时器，使用更长的间隔
                    recovery_interval = min(interval * 2, 300)  # 最长5分钟
                    recovery_timer = threading.Timer(
                        recovery_interval,
                        self._timer_callback,
                        args=(timer_name, interval, callback, args, kwargs),
                    )
                    recovery_timer.daemon = True

                    # 更新定时器信息
                    self._timers[timer_name]["timer"] = recovery_timer
                    self._timers[timer_name]["next_run"] = (
                        time.time() + recovery_interval
                    )

                    # 启动恢复定时器
                    recovery_timer.start()
                    logger.warning(
                        f"定时器 {timer_name} 出错后重新启动，使用恢复间隔: {recovery_interval}秒"
                    )

                except Exception as recovery_error:
                    logger.error(
                        f"重新启动定时器 {timer_name} 失败: {str(recovery_error)}"
                    )

    def cancel_timer(self, timer_name: str) -> bool:
        """
        取消定时器

        Args:
            timer_name: 定时器名称

        Returns:
            bool: 是否成功取消定时器
        """
        if timer_name in self._timers:
            try:
                # 取消定时器
                self._timers[timer_name]["timer"].cancel()
                # 从字典中删除
                del self._timers[timer_name]
                logger.debug(f"定时器 {timer_name} 已取消")
                return True
            except Exception as e:
                logger.error(f"取消定时器 {timer_name} 失败: {str(e)}")
        return False

    def get_timer_info(self, timer_name: str) -> Optional[Dict[str, Any]]:
        """
        获取定时器信息

        Args:
            timer_name: 定时器名称

        Returns:
            Optional[Dict[str, Any]]: 定时器信息
        """
        return self._timers.get(timer_name)

    def shutdown(self, timeout: int = 5) -> None:
        """
        关闭所有定时器和线程池

        Args:
            timeout: 关闭超时时间（秒），默认5秒
        """
        # 记录关闭开始时间
        shutdown_start = time.time()

        # 设置关闭标志
        self.is_shutting_down = True

        # 取消所有定时器，设置单独的超时
        timer_timeout = min(2, timeout / 2)  # 分配一部分超时时间给定时器取消
        timer_names = list(self._timers.keys())
        if timer_names:
            logger.debug(
                f"正在取消 {len(timer_names)} 个定时器: {', '.join(timer_names)}"
            )
            for timer_name in timer_names:
                try:
                    # 检查是否超时
                    if time.time() - shutdown_start > timer_timeout:
                        logger.warning(
                            f"取消定时器超时，跳过剩余 {len(timer_names) - timer_names.index(timer_name)} 个定时器"
                        )
                        break

                    self.cancel_timer(timer_name)
                except Exception as e:
                    logger.warning(f"取消定时器 {timer_name} 时出现警告: {str(e)}")

        # 计算剩余的超时时间
        remaining_timeout = max(0.5, timeout - (time.time() - shutdown_start))

        # 关闭线程池 - 使用更安全的方法
        if self._executor:
            try:
                # 尝试优雅关闭，使用剩余的超时时间
                logger.debug(
                    f"正在关闭定时器管理器线程池（超时: {remaining_timeout:.1f}秒）..."
                )

                # 先尝试取消所有未完成的任务
                for future in list(getattr(self._executor, "_work_queue", [])):
                    try:
                        future.cancel()
                    except:
                        pass

                # 关闭线程池
                self._executor.shutdown(wait=True, cancel_futures=True)
                logger.info(
                    f"定时器管理器线程池已关闭，总耗时: {time.time() - shutdown_start:.1f}秒"
                )
            except Exception as e:
                # 如果优雅关闭失败，尝试强制关闭
                logger.warning(f"优雅关闭线程池失败: {str(e)}，尝试强制关闭")
                try:
                    self._executor.shutdown(wait=False)
                    logger.info("定时器管理器线程池已强制关闭")
                except Exception as e2:
                    logger.error(f"强制关闭线程池失败: {str(e2)}")
            finally:
                # 确保线程池引用被清除
                self._executor = None
