version: "3.8"
services:
  dji_port:
    build:
      context: .
      dockerfile: dji_port/Dockerfile
    container_name: dji_port
    depends_on:
      - redis
    environment:
      IDSPACE_CONFIG: /app/config/conn_config.json
    volumes:
      - ./config:/app/config
    networks:
      - idspace_net

  routeplan:
    build:
      context: .
      dockerfile: routeplan/Dockerfile
    container_name: routeplan
    depends_on:
      - redis
    environment:
      IDSPACE_CONFIG: /app/config/conn_config.json
    volumes:
      - ./config:/app/config
    networks:
      - idspace_net


  sora:
    build:
      context: .
      dockerfile: sora/Dockerfile
    container_name: sora
    depends_on:
      - redis
    environment:
      IDSPACE_CONFIG: /app/config/conn_config.json
    volumes:
      - ./config:/app/config
    networks:
      - idspace_net

  redis:
    image: redis:6.2
    container_name: redis
    ports:
      - "6379:6379"
    networks:
      - idspace_net

networks:
  idspace_net:
    driver: bridge
