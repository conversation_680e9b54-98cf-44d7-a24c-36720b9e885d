import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.colors as mcolors
import time
from typing import Dict, List, Tuple, Optional, Set
import threading


class FlightPathVisualizer:
    """
    可视化飞行路径和禁飞区的类
    """

    _instance = None
    _lock = threading.Lock()

    def __new__(cls, *args, **kwargs):
        """单例模式实现"""
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(FlightPathVisualizer, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance

    def __init__(self, map_3d=None):
        """
        初始化可视化器

        Args:
            map_3d: 3D地图实例
        """
        # 单例模式下只初始化一次
        if self._initialized:
            return

        self.map = map_3d
        self.fig = None
        self.ax = None
        self.paths = {}  # 存储所有路径 {flight_id: grid_path}
        self.colors = list(mcolors.TABLEAU_COLORS.values())  # 颜色列表
        self.last_update_time = 0
        self.is_visible = False
        self._initialized = True

    def initialize_plot(self):
        """初始化绘图窗口"""
        if self.fig is None:
            self.fig = plt.figure(figsize=(12, 10))
            self.ax = self.fig.add_subplot(111, projection="3d")

            # 设置坐标轴标签
            self.ax.set_xlabel("X")
            self.ax.set_ylabel("Y")
            self.ax.set_zlabel("Z")

            # 如果有地图实例，设置坐标轴范围
            if self.map:
                self.ax.set_xlim(0, self.map.width)
                self.ax.set_ylim(0, self.map.height)
                self.ax.set_zlim(0, self.map.depth)

            # 启用网格
            self.ax.grid(True)

            # 启用交互模式
            plt.ion()

    def add_path(self, flight_id: str, grid_path: List[Tuple[int, int, int]]):
        """
        添加一条飞行路径

        Args:
            flight_id: 航班ID
            grid_path: 网格坐标路径列表，格式为[(y,x,z), ...]
        """
        self.paths[flight_id] = grid_path

    def remove_path(self, flight_id: str):
        """
        移除一条飞行路径

        Args:
            flight_id: 航班ID
        """
        if flight_id in self.paths:
            del self.paths[flight_id]

    def clear_paths(self):
        """清空所有路径"""
        self.paths.clear()

    def plot_no_fly_zones(self):
        """绘制所有禁飞区"""
        if not self.map:
            return

        # 绘制所有禁飞区
        no_fly_zone_types = {
            name: info
            for name, info in self.map.obstacle_manager.get_all_types().items()
            if not name.startswith("fixed")
        }

        for zone_name, zone_info in no_fly_zone_types.items():
            zone_positions = self.map.obstacle_manager.get_positions(zone_name)
            if zone_positions:
                # 转换为列表并稀疏采样
                positions_list = list(zone_positions)
                sampled_positions = positions_list[::10]  # 每10个点取1个

                # 如果采样后点数过少，至少保留3个点
                if len(sampled_positions) < 3 and len(positions_list) >= 3:
                    sampled_positions = [
                        positions_list[0],
                        positions_list[len(positions_list) // 2],
                        positions_list[-1],
                    ]

                if sampled_positions:
                    positions = np.array(sampled_positions)
                    self.ax.scatter(
                        positions[:, 1],  # x coordinates
                        positions[:, 0],  # y coordinates
                        positions[:, 2],  # z coordinates
                        marker="s",
                        color="red",
                        alpha=0.05,
                        s=1,
                        label=f"No-fly zone: {zone_name}",
                    )

    def plot_paths(self):
        """绘制所有飞行路径"""
        used_labels = set()  # 避免重复标签

        for i, (flight_id, path) in enumerate(self.paths.items()):
            if not path:
                continue

            # 选择颜色
            color = self.colors[i % len(self.colors)]

            # 转换为numpy数组
            points = np.array(path)
            if len(points) > 0:
                # 绘制路径线
                self.ax.plot(
                    points[:, 1],  # x coordinates
                    points[:, 0],  # y coordinates
                    points[:, 2],  # z coordinates
                    color=color,
                    marker="o",
                    markersize=2,
                    label=(
                        f"Flight {flight_id}" if flight_id not in used_labels else None
                    ),
                )
                used_labels.add(flight_id)

                # 标记起点和终点
                self.ax.scatter(
                    [points[0, 1]],
                    [points[0, 0]],
                    [points[0, 2]],
                    color=color,
                    marker="^",
                    s=100,
                )
                self.ax.scatter(
                    [points[-1, 1]],
                    [points[-1, 0]],
                    [points[-1, 2]],
                    color=color,
                    marker="*",
                    s=100,
                )

    def update_visualization(self):
        """更新可视化"""
        # 初始化绘图窗口
        self.initialize_plot()

        # 清空当前图形
        self.ax.cla()

        # 重新设置坐标轴标签
        self.ax.set_xlabel("X")
        self.ax.set_ylabel("Y")
        self.ax.set_zlabel("Z")

        # 如果有地图实例，设置坐标轴范围
        if self.map:
            self.ax.set_xlim(0, self.map.width)
            self.ax.set_ylim(0, self.map.height)
            self.ax.set_zlim(0, self.map.depth)

        # 绘制禁飞区
        self.plot_no_fly_zones()

        # 绘制路径
        self.plot_paths()

        # 添加图例
        self.ax.legend(loc="upper right")

        # 设置标题
        self.ax.set_title("Flight Paths and No-fly Zones")

        # 更新图形
        plt.tight_layout()
        self.fig.canvas.draw()
        self.fig.canvas.flush_events()

        # 标记为可见
        self.is_visible = True

    def show(self):
        """显示可视化窗口"""
        self.update_visualization()
        plt.show(block=False)

    def close(self):
        """关闭可视化窗口"""
        if self.fig:
            plt.close(self.fig)
            self.fig = None
            self.ax = None
            self.is_visible = False
