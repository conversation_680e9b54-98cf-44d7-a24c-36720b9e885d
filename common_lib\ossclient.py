import boto3
from loguru import logger
from botocore.exceptions import NoCredentialsError, PartialCredentialsError, EndpointConnectionError, ClientError

class OssClient():
    """
    oss网盘连接权限管理及相关接口
    """
    def __init__(self, ops_conf) -> None:
        """
        初始化函数，用于配置MinIO相关的参数并创建STS客户端。

        Args:
            ops_conf (dict): 包含MinIO配置的字典。字典中应包含"minio"键，其值为MinIO的配置字典。
                            如果"minio"键不存在，则使用空字典作为默认值。
        """
        # 从ops_conf中获取MinIO配置，如果不存在则使用空字典
        minio_conf = ops_conf.get("minio", {})

        # 从ops_conf中获取Oss配置，如果不存在则使用空字典
        oss_conf = ops_conf.get("oss", {})
        
        
        # 从MinIO配置中提取必要的参数
        endpoint = oss_conf.get("endpoint")
        access_key = minio_conf.get("access_key")
        secret_key = minio_conf.get("secret_key")
        region = oss_conf.get("region")

        # 从MinIO配置中提取可选参数并赋值给实例变量
        self.role_arn = minio_conf.get("role_arn")
        self.role_session_name = minio_conf.get("role_session_name")
        self.expire = minio_conf.get("expire")
        
        # 检查是否缺少必要的MinIO配置，如果缺少则抛出异常
        if not all([endpoint, access_key, secret_key, region]):
            self.sts_client = None
            return

        # 使用提取的配置参数创建AWS Session和STS客户端
        self.session = boto3.Session(
            aws_access_key_id=access_key,
            aws_secret_access_key=secret_key,
            region_name=region
        )
        self.sts_client = self.session.client('sts', endpoint_url=endpoint)

    @logger.catch
    def get_credentials(self):
        """
        获取临时安全凭据。

        该方法通过调用AWS STS服务的AssumeRole接口，生成临时的安全凭据。这些凭据包括访问密钥ID、访问密钥秘钥和安全令牌，
        以及凭据的过期时间。这些凭据通常用于临时访问AWS资源。

        返回:
            dict: 包含以下键的字典：
                - access_key_id (str): 临时访问密钥ID。
                - access_key_secret (str): 临时访问密钥秘钥。
                - security_token (str): 临时安全令牌。
                - expire (str): 凭据的过期时间。

        异常:
            如果在获取凭据过程中发生错误（如凭据错误、部分凭据错误、连接错误或客户端错误），
            将记录错误日志并重新抛出异常。
        """
        try:
            # 调用AssumeRole生成临时凭据
            response = self.sts_client.assume_role(
                RoleArn=self.role_arn,
                RoleSessionName=self.role_session_name
            )
            
            # 从响应中提取凭据信息
            credentials = response['Credentials']
            
            # 构建返回的凭据数据
            data = {
                "access_key_id": credentials['AccessKeyId'],
                "access_key_secret": credentials['SecretAccessKey'],
                "security_token": credentials['SessionToken'],
                "expire": self.expire
            }
            return data

        except (NoCredentialsError, PartialCredentialsError, EndpointConnectionError, ClientError) as e:
            # 记录错误日志并重新抛出异常
            logger.error(f"Error in get_credentials with RoleArn={self.role_arn}, RoleSessionName={self.role_session_name}: {e}")
            raise
        