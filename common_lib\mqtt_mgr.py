# -*- coding: utf-8 -*-
""" MQTT客户端连接使用函数库
python 版本：python3
Name：mqtt_mgr.py
Description:MQTT客户端连接使用函数库
# Author:胡旭
# Version:1.0
# Date:2024/9/12
"""

import paho.mqtt.client as mqtt
import ssl
from loguru import logger

class MqttClient:
    def __init__(self, client_id, broker_host, broker_port=8883, keep_alive=60, use_ssl=True, username=None, password=None):
        """
        初始化MQTT客户端连接。

        参数:
        - client_id (str): 客户端ID，用于标识MQTT客户端。
        - broker_host (str): MQTT代理服务器的主机地址。
        - broker_port (int, 可选): MQTT代理服务器的端口号，默认为8883。
        - keep_alive (int, 可选): 客户端与服务器之间的心跳间隔时间（秒），默认为60秒。
        - use_ssl (bool, 可选): 是否使用SSL/TLS加密连接，默认为True。
        - username (str, 可选): 用于连接MQTT代理的用户名，默认为None。
        - password (str, 可选): 用于连接MQTT代理的密码，默认为None。
        """
        # 初始化MQTT客户端实例
        self.client = mqtt.Client(client_id = client_id)
        self.broker_host = broker_host
        self.broker_port = broker_port
        self.keep_alive = keep_alive
        self.topics = []  # 存储订阅的主题列表
        self.messages = []  # 存储接收到的消息列表
        
        # 设置MQTT客户端的回调函数
        self.client.on_connect = self._on_connect
        self.client.on_message = self._on_message
        self.client.on_disconnect = self._on_disconnect
        
        # 如果使用SSL/TLS，配置TLS参数
        if use_ssl:
            ca_certs_path = 'config/to/emqxsl-ca.crt'  # CA证书路径
            certfile_path = 'config/to/client.crt'  # 客户端证书路径
            keyfile_path = 'config/to/client.key'  # 客户端私钥路径
            
            # 配置TLS参数，包括CA证书、客户端证书、私钥等
            self.client.tls_set(
                ca_certs=ca_certs_path,
                certfile=None,
                keyfile=None,
                cert_reqs=ssl.CERT_REQUIRED,
                tls_version=ssl.PROTOCOL_TLSv1_2,
                ciphers=None
            )
            # 设置是否验证服务器证书
            self.client.tls_insecure_set(True)

        # 如果提供了用户名和密码，设置MQTT客户端的认证信息
        if username and password:
            self.client.username_pw_set(username, password)

    @logger.catch
    def _on_connect(self, client, userdata, flags, rc):
        """
        当客户端成功连接到MQTT代理时调用。

        参数:
        - client: MQTT客户端实例，用于与代理进行交互。
        - userdata: 用户自定义数据，通常为None或用户传递的数据。
        - flags: 连接标志，包含代理返回的连接相关信息。
        - rc: 连接结果代码，0表示连接成功，其他值表示连接失败。

        返回值:
        无
        """
        if rc == 0:
            logger.debug("Connected to MQTT broker")
            # 遍历所有主题并尝试订阅
            for topic in self.topics:
                try:
                    self.client.subscribe(topic)
                except Exception as e:
                    logger.error(f"Failed to subscribe to topic {topic}: {e}")
        else:
            logger.error(f"Failed to connect to MQTT broker, return code {rc}")

    @logger.catch
    def _on_message(self, client, userdata, msg):
        """
        处理接收到的消息。

        当接收到消息时，此方法会被调用。如果消息不为空，则将其添加到消息列表中。

        参数:
        - client: MQTT客户端实例，用于与MQTT代理进行通信。
        - userdata: 用户自定义数据，通常为None或用户传递的上下文信息。
        - msg: 接收到的消息对象，包含主题、负载等信息。

        返回值:
        无
        """
        # 检查消息是否为空，若不为空则将其添加到消息列表中
        if msg is not None:
            self.messages.append(msg)

    @logger.catch
    def _on_disconnect(self, client, userdata, rc):
        """
        处理MQTT客户端断开连接时的回调函数。

        参数:
        - client: MQTT客户端实例，表示当前连接的客户端。
        - userdata: 用户自定义数据，通常为None或用户传递的数据。
        - rc: 返回码，表示断开连接的原因。0表示正常断开，非0表示异常断开。

        返回值:
        无返回值。
        """
        # 如果返回码不为0，表示异常断开，记录调试信息
        if rc != 0:
            logger.debug(f"Unexpected disconnection from MQTT broker, return code {rc}")

    @logger.catch
    def connect(self):
        """
        连接到MQTT代理服务器并启动消息循环。

        该方法尝试连接到指定的MQTT代理服务器，并在连接成功后启动消息循环。
        如果连接失败，将记录错误日志。

        参数:
            无

        返回值:
            无
        """
        try:
            # 尝试连接到MQTT代理服务器
            self.client.connect(self.broker_host, self.broker_port, self.keep_alive)
            # 启动消息循环以处理传入的消息
            self.client.loop_start()
            # 记录连接成功的调试信息
            logger.debug("MQTT Connected successfully")
        except Exception as e:
            # 记录连接失败的错误信息
            logger.error(f"Failed to connect MQTT: {e}")

    @logger.catch
    def disconnect(self):
        """
        断开MQTT连接并停止事件循环。

        该方法尝试断开MQTT客户端的连接，并停止事件循环。如果操作成功，记录调试信息；
        如果操作失败，记录错误信息。

        参数:
            无

        返回值:
            无
        """
        try:
            # 尝试断开MQTT连接并停止事件循环
            self.client.disconnect()
            self.client.loop_stop()
            logger.debug("MQTT Disconnected successfully")
        except Exception as e:
            # 如果断开连接或停止事件循环失败，记录错误信息
            logger.error(f"Failed to disconnect MQTT: {e}")

    @logger.catch
    def subscribe(self, topics):
        """
        订阅指定的主题。

        参数:
        - topics: 要订阅的主题，可以是字符串或字符串列表。如果为None，则不进行任何操作。

        返回值:
        无返回值。
        """
        # 如果topics不为None，则将其添加到self.topics中
        if topics is not None:
            if isinstance(topics, list): 
                self.topics.extend(topics)
            elif isinstance(topics, str): 
                self.topics.append(topics)
        
        # 如果客户端已连接，则尝试订阅每个主题
        if self.client.is_connected():
            for topic in topics:
                try:
                    self.client.subscribe(topic)
                except Exception as e:
                    # 如果订阅失败，记录错误日志
                    logger.error(f"Failed to subscribe to topic {topic}: {e}")

    @logger.catch
    def publish(self, topic, message):
        """
        发布消息到指定的主题。

        参数:
        - topic (str): 消息发布的目标主题。必须为字符串类型。
        - message: 要发布的消息内容。

        异常:
        - TypeError: 如果 `topic` 不是字符串类型，则抛出此异常。
        - Exception: 如果发布消息时发生错误，记录错误日志但不中断程序执行。
        """
        # 检查 topic 是否为字符串类型
        if not isinstance(topic, str):
            raise TypeError("Topic must be a string")
        
        # 尝试发布消息，捕获并记录可能的异常
        try:
            self.client.publish(topic, message)
        except Exception as e:
            logger.error(f"Failed to publish message: {e}")

# 使用示例
if __name__ == "__main__":
    from pack import tools
    tools.init_log("recieve_data")
    client = MqttClient("my_client", "192.168.31.252")
    client.connect()

    # 订阅主题
    client.subscribe(["test/topic1", "test/topic2"])

    # 发布消息
    client.publish("test/topic1", "Hello MQTT!")

    # 运行一段时间后断开连接
    import time
    time.sleep(10)
    client.disconnect()