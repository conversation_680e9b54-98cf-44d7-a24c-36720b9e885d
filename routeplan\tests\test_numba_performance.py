# -*- coding: utf-8 -*-
"""
Numba性能测试脚本
比较原始实现和Numba加速实现的性能差异
"""

import time
import numpy as np
import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

try:
    from routeplan.src.core.pathfinding.numba_accelerated import (
        manhattan_distance_numba,
        octile_distance_numba,
        euclidean_distance_numba,
        euclidean_distance_squared_numba,
        moving_average_weights_numba,
        weighted_average_position_numba,
    )
    NUMBA_AVAILABLE = True
    print("✓ Numba加速函数导入成功")
except ImportError as e:
    NUMBA_AVAILABLE = False
    print(f"✗ Numba不可用: {e}")

# 原始实现函数（用于对比）
def manhattan_distance_original(pos1, pos2):
    return abs(pos2[0] - pos1[0]) + abs(pos2[1] - pos1[1]) + abs(pos2[2] - pos1[2])

def octile_distance_original(pos1, pos2):
    dy = abs(pos2[0] - pos1[0])
    dx = abs(pos2[1] - pos1[1])
    dz = abs(pos2[2] - pos1[2])
    diag_xy = min(dx, dy)
    straight_xy = max(dx, dy) - diag_xy
    return diag_xy * 1.414 + straight_xy + dz

def euclidean_distance_original(pos1, pos2):
    import math
    return math.sqrt(
        (pos2[0] - pos1[0]) ** 2 + 
        (pos2[1] - pos1[1]) ** 2 + 
        (pos2[2] - pos1[2]) ** 2
    )

def euclidean_distance_squared_original(pos1, pos2):
    return (
        (pos1[0] - pos2[0]) ** 2 +
        (pos1[1] - pos2[1]) ** 2 +
        (pos1[2] - pos2[2]) ** 2
    )

def moving_average_weights_original(window_size):
    import math
    weights = np.exp(-0.5 * np.square(np.linspace(-1, 1, window_size)))
    return weights / np.sum(weights)

def benchmark_function(func_name, original_func, numba_func, test_data, iterations=10000):
    """对比函数性能"""
    print(f"\n=== {func_name} 性能测试 ===")
    
    # 预热（特别重要对于Numba函数）
    if NUMBA_AVAILABLE:
        for data in test_data[:10]:
            numba_func(*data)
    
    # 测试原始实现
    start_time = time.perf_counter()
    for _ in range(iterations):
        for data in test_data:
            original_func(*data)
    original_time = time.perf_counter() - start_time
    
    # 测试Numba实现
    numba_time = float('inf')
    if NUMBA_AVAILABLE:
        start_time = time.perf_counter()
        for _ in range(iterations):
            for data in test_data:
                numba_func(*data)
        numba_time = time.perf_counter() - start_time
    
    # 输出结果
    print(f"原始实现: {original_time:.6f}s")
    if NUMBA_AVAILABLE:
        print(f"Numba实现: {numba_time:.6f}s")
        speedup = original_time / numba_time if numba_time > 0 else 0
        print(f"加速比: {speedup:.2f}x")
        print(f"性能提升: {((original_time - numba_time) / original_time * 100):.1f}%")
    else:
        print("Numba不可用，无法进行对比")

def generate_test_data(count=100):
    """生成测试数据"""
    np.random.seed(42)  # 确保结果可重现
    positions = []
    for _ in range(count):
        pos1 = (
            float(np.random.randint(0, 1000)),
            float(np.random.randint(0, 1000)),
            float(np.random.randint(0, 100))
        )
        pos2 = (
            float(np.random.randint(0, 1000)),
            float(np.random.randint(0, 1000)),
            float(np.random.randint(0, 100))
        )
        positions.append((pos1, pos2))
    return positions

def test_weighted_average_performance():
    """测试加权平均计算性能"""
    print(f"\n=== 加权平均计算性能测试 ===")
    
    # 生成测试数据
    np.random.seed(42)
    window_sizes = [5, 10, 15, 20]
    iterations = 1000
    
    for window_size in window_sizes:
        print(f"\n窗口大小: {window_size}")
        
        # 生成位置数组
        positions = np.random.rand(window_size, 3) * 1000
        
        # 原始实现
        start_time = time.perf_counter()
        for _ in range(iterations):
            weights = moving_average_weights_original(window_size)
            # 模拟原始的加权平均计算
            y_sum = np.sum(positions[:, 0] * weights)
            x_sum = np.sum(positions[:, 1] * weights)
            z_sum = np.sum(positions[:, 2] * weights)
        original_time = time.perf_counter() - start_time
        
        # Numba实现
        if NUMBA_AVAILABLE:
            # 预热
            weights_numba = moving_average_weights_numba(window_size)
            weighted_average_position_numba(positions, weights_numba)
            
            start_time = time.perf_counter()
            for _ in range(iterations):
                weights_numba = moving_average_weights_numba(window_size)
                y_sum, x_sum, z_sum = weighted_average_position_numba(positions, weights_numba)
            numba_time = time.perf_counter() - start_time
            
            print(f"原始实现: {original_time:.6f}s")
            print(f"Numba实现: {numba_time:.6f}s")
            speedup = original_time / numba_time if numba_time > 0 else 0
            print(f"加速比: {speedup:.2f}x")
        else:
            print(f"原始实现: {original_time:.6f}s")
            print("Numba不可用")

def run_all_benchmarks():
    """运行所有性能测试"""
    print("🚀 开始性能测试...")
    print(f"Numba可用: {'是' if NUMBA_AVAILABLE else '否'}")
    
    # 生成测试数据
    test_data = generate_test_data(100)
    iterations = 1000
    
    # 距离计算函数测试
    if NUMBA_AVAILABLE:
        benchmark_function(
            "曼哈顿距离", 
            manhattan_distance_original, 
            manhattan_distance_numba,
            test_data, 
            iterations
        )
        
        benchmark_function(
            "八方向距离", 
            octile_distance_original, 
            octile_distance_numba,
            test_data, 
            iterations
        )
        
        benchmark_function(
            "欧几里得距离", 
            euclidean_distance_original, 
            euclidean_distance_numba,
            test_data, 
            iterations
        )
        
        benchmark_function(
            "欧几里得距离平方", 
            euclidean_distance_squared_original, 
            euclidean_distance_squared_numba,
            test_data, 
            iterations
        )
    
    # 加权平均性能测试
    test_weighted_average_performance()
    
    print(f"\n🎉 性能测试完成!")
    if NUMBA_AVAILABLE:
        print("\n📊 总结:")
        print("- 距离计算函数通过Numba JIT编译显著提升了性能")
        print("- 对于大量重复调用的数值计算，Numba可以提供2-10倍的性能提升")
        print("- 首次调用会有JIT编译开销，但后续调用会很快")
        print("- 建议在程序启动时预热Numba函数以减少首次调用延迟")
    else:
        print("\n⚠️  提示:")
        print("安装Numba以获得显著的性能提升:")
        print("pip install numba")

if __name__ == "__main__":
    run_all_benchmarks()
