import json
import os
import time
import argparse
import io
from pyproj import Geod
import zipfile
from xml.etree.ElementTree import Element, SubElement, tostring
from xml.dom import minidom


def read_json(input_file):
    """
    将包含地理数据的JSON文件转换为KML格式
    :param input_file: 输入JSON文件路径
    :return python_dict: 输出dict
    """
    try:
        # 读取JSON数据
        with open(input_file, "r", encoding="utf-8") as f:
            data = json.load(f)
    except Exception as e:
        print(f"读取JSON文件失败: {e}")
        return {}
    return data


def json_to_kml(uav_data):
    """
    将包含地理数据的JSON文件转换为KML格式
    :param uav_data: 输入JSON文件路径
    :param output_file: 输出KML文件路径
    """

    first_height = uav_data["planned_path_points"][0][3]
    # 创建KML根元素
    kml = Element("kml")
    kml.set("xmlns", "http://www.opengis.net/kml/2.2")
    kml.set("xmlns:wpml", "http://www.dji.com/wpmz/1.0.6")
    document = SubElement(kml, "Document")
    SubElement(document, "wpml:author").text = "ysh"
    SubElement(document, "wpml:createTime").text = str(
        int(
            time.mktime(time.strptime(uav_data["takeoff_time"], "%Y-%m-%d %H:%M:%S"))
            * 1000
        )
    )
    SubElement(document, "wpml:updateTime").text = str(int(time.time() * 1000))
    missionConfig = SubElement(document, "wpml:missionConfig")
    SubElement(missionConfig, "wpml:flyToWaylineMode").text = "safely"
    SubElement(missionConfig, "wpml:finishAction").text = "goHome"
    SubElement(missionConfig, "wpml:exitOnRCLost").text = "executeLostAction"
    SubElement(missionConfig, "wpml:executeRCLostAction").text = "goBack"
    SubElement(missionConfig, "wpml:takeOffSecurityHeight").text = str(first_height)
    # SubElement(missionConfig, 'wpml:takeOffRefPoint').text = "38.944722,115.946443,-2.725786"
    # SubElement(missionConfig, 'wpml:takeOffRefPointAGLHeight').text = "0"
    SubElement(missionConfig, "wpml:globalTransitionalSpeed").text = "15"
    SubElement(missionConfig, "wpml:globalRTHHeight").text = "100"

    droneInfo = SubElement(missionConfig, "wpml:droneInfo")
    SubElement(droneInfo, "wpml:droneEnumValue").text = "100"
    SubElement(droneInfo, "wpml:droneSubEnumValue").text = "0"
    SubElement(missionConfig, "wpml:waylineAvoidLimitAreaMode").text = str(0)

    payloadInfo = SubElement(missionConfig, "wpml:payloadInfo")
    SubElement(payloadInfo, "wpml:payloadEnumValue").text = "89"
    SubElement(payloadInfo, "wpml:payloadSubEnumValue").text = "2"
    SubElement(payloadInfo, "wpml:payloadPositionIndex").text = "0"

    Folder = SubElement(document, "Folder")
    SubElement(Folder, "wpml:templateType").text = "waypoint"
    SubElement(Folder, "wpml:templateId").text = "0"
    waylineCoordinateSysParam = SubElement(Folder, "wpml:waylineCoordinateSysParam")
    SubElement(waylineCoordinateSysParam, "wpml:coordinateMode").text = "WGS84"
    SubElement(waylineCoordinateSysParam, "wpml:heightMode").text = "aboveGroundLevel"
    SubElement(Folder, "wpml:autoFlightSpeed").text = "10"
    SubElement(Folder, "wpml:globalHeight").text = "100"
    SubElement(Folder, "wpml:caliFlightEnable").text = "0"  # 是否开启标定飞行
    SubElement(Folder, "wpml:gimbalPitchMode").text = "manual"  # 云台俯仰角控制模式
    globalWaypointHeadingParam = SubElement(
        Folder, "wpml:globalWaypointHeadingParam"
    )  # 全局偏航角模式参数
    SubElement(
        globalWaypointHeadingParam, "wpml:waypointHeadingMode"
    ).text = "manually"  # 飞行器偏航角模式
    SubElement(
        globalWaypointHeadingParam, "wpml:waypointHeadingAngle"
    ).text = "0"  # 飞行器偏航角度
    SubElement(
        globalWaypointHeadingParam, "wpml:waypointPoiPoint"
    ).text = "0.000000,0.000000,0.000000"  # 兴趣点
    SubElement(
        globalWaypointHeadingParam, "wpml:waypointHeadingPathMode"
    ).text = "followBadArc"  # 飞行器偏航角转动方向
    SubElement(
        globalWaypointHeadingParam, "wpml:waypointHeadingPoiIndex"
    ).text = "0"  # 全局偏航角模式参数
    SubElement(
        Folder, "wpml:globalWaypointTurnMode"
    ).text = (
        "toPointAndStopWithDiscontinuityCurvature"  # 全局航点类型（全局航点转弯模式）
    )
    SubElement(
        Folder, "wpml:globalUseStraightLine"
    ).text = "1"  # 全局航段轨迹是否尽量贴合直线
    payloadParam = SubElement(
        Folder, "wpml:payloadParam"
    )  # 全局航段轨迹是否尽量贴合直线
    SubElement(payloadParam, "wpml:payloadPositionIndex").text = "0"  #  负载挂载位置
    SubElement(payloadParam, "wpml:imageFormat").text = "ir,visable"  # 图片格式列表
    for point in uav_data["planned_path_points"]:
        Placemark = SubElement(Folder, "Placemark")
        Point = SubElement(Placemark, "Point")
        SubElement(Point, "coordinates").text = "{},{}".format(
            round(point[1], 6), round(point[2], 6)
        )
        SubElement(Placemark, "wpml:index").text = str(point[0])  # 航点序号
        SubElement(Placemark, "wpml:ellipsoidHeight").text = str(
            point[3]
        )  # 航点高度（WGS84椭球高度）
        SubElement(Placemark, "wpml:height").text = str(
            point[3]
        )  # 航点高度（EGM96海拔高度
        SubElement(Placemark, "wpml:useGlobalHeight").text = "1"  # 是否使用全局高度
        SubElement(Placemark, "wpml:useGlobalSpeed").text = "1"  # 是否使用全局飞行速度
        SubElement(Placemark, "wpml:waypointSpeed").text = "10"  # 航点飞行速度
        SubElement(
            Placemark, "wpml:useGlobalHeadingParam"
        ).text = "1"  # 是否使用全局偏航角模式参数
        waypointHeadingParam = SubElement(
            Placemark, "wpml:waypointHeadingParam"
        )  # 偏航角模式参数
        SubElement(
            waypointHeadingParam, "wpml:waypointHeadingMode"
        ).text = "followWayline"  # 飞行器偏航角模式
        SubElement(
            waypointHeadingParam, "wpml:waypointHeadingAngle"
        ).text = "0"  # 飞行器偏航角度
        SubElement(
            waypointHeadingParam, "wpml:waypointPoiPoint"
        ).text = "0.000000,0.000000,0.000000"  # 兴趣点
        SubElement(
            waypointHeadingParam, "wpml:waypointHeadingPathMode"
        ).text = "followBadArc"  # 飞行器偏航角转动方向
        SubElement(
            waypointHeadingParam, "wpml:waypointHeadingPoiIndex"
        ).text = "0"  # 飞行器偏航角转动方向
        SubElement(
            Placemark, "wpml:useGlobalTurnParam"
        ).text = "1"  # 是否使用全局航点类型（全局航点转弯模式）
        waypointTurnParam = SubElement(Placemark, "wpml:waypointTurnParam")
        SubElement(
            waypointTurnParam, "wpml:waypointTurnMode"
        ).text = "toPointAndStopWithDiscontinuityCurvature"  # 全局航点类型（全局航点转弯模式）
        SubElement(
            waypointTurnParam, "wpml:waypointTurnDampingDist"
        ).text = "0.2"  # 全局航点类型（全局航点转弯模式）
        SubElement(Placemark, "wpml:useStraightLine").text = "1"  # 该航段是否贴合直线
        SubElement(Placemark, "wpml:isRisky").text = "0"  # 是否危险点

    # 生成格式化的XML
    xml_str = minidom.parseString(tostring(kml)).toprettyxml(indent="  ")
    return xml_str


def json_to_wpml(uav_data):
    """
    将包含地理数据的JSON文件转换为KML格式
    :param input_file: 输入JSON文件路径
    :param output_file: 输出KML文件路径
    """

    first_height = uav_data["planned_path_points"][0][3]
    # 创建KML根元素
    kml = Element("kml")
    kml.set("xmlns", "http://www.opengis.net/kml/2.2")
    kml.set("xmlns:wpml", "http://www.dji.com/wpmz/1.0.6")
    document = SubElement(kml, "Document")
    missionConfig = SubElement(document, "wpml:missionConfig")
    SubElement(missionConfig, "wpml:flyToWaylineMode").text = "safely"
    SubElement(missionConfig, "wpml:finishAction").text = "goHome"
    SubElement(missionConfig, "wpml:exitOnRCLost").text = "executeLostAction"
    SubElement(missionConfig, "wpml:executeRCLostAction").text = "goBack"
    SubElement(missionConfig, "wpml:takeOffSecurityHeight").text = str(first_height)
    SubElement(missionConfig, "wpml:globalTransitionalSpeed").text = "15"
    SubElement(missionConfig, "wpml:globalRTHHeight").text = "100"
    # SubElement(missionConfig, 'wpml:takeOffRefPoint').text = "22.579831,113.937935,32.536774"
    # SubElement(missionConfig, 'wpml:takeOffRefPointAGLHeight').text = "5.336929169"
    droneInfo = SubElement(missionConfig, "wpml:droneInfo")
    SubElement(droneInfo, "wpml:droneEnumValue").text = "100"
    SubElement(droneInfo, "wpml:droneSubEnumValue").text = "0"
    SubElement(missionConfig, "wpml:waylineAvoidLimitAreaMode").text = str(0)

    payloadInfo = SubElement(missionConfig, "wpml:payloadInfo")
    SubElement(payloadInfo, "wpml:payloadEnumValue").text = "89"
    SubElement(payloadInfo, "wpml:payloadSubEnumValue").text = "2"
    SubElement(payloadInfo, "wpml:payloadPositionIndex").text = "0"

    Folder = SubElement(document, "Folder")
    # SubElement(Folder, 'wpml:templateType').text = "waypoint"
    SubElement(Folder, "wpml:templateId").text = "0"
    SubElement(Folder, "wpml:executeHeightMode").text = "WGS84"
    SubElement(Folder, "wpml:waylineId").text = "0"
    # SubElement(Folder, 'wpml:distance').text = "803.561096191406"
    # SubElement(Folder, 'wpml:duration').text = "91.9275360107422"
    SubElement(Folder, "wpml:autoFlightSpeed").text = "10"

    for point in uav_data["planned_path_points"]:
        Placemark = SubElement(Folder, "Placemark")
        Point = SubElement(Placemark, "Point")
        SubElement(Point, "coordinates").text = "{},{}".format(
            round(point[1], 6), round(point[2], 6)
        )

        SubElement(Placemark, "wpml:index").text = str(point[0])  # 航点序号
        SubElement(Placemark, "wpml:executeHeight").text = str(
            point[3]
        )  # 航点高度（WGS84椭球高度）
        SubElement(Placemark, "wpml:waypointSpeed").text = "10"  # 航点飞行速度
        waypointHeadingParam = SubElement(
            Placemark, "wpml:waypointHeadingParam"
        )  # 偏航角模式参数
        SubElement(
            waypointHeadingParam, "wpml:waypointHeadingMode"
        ).text = "followWayline"  # 飞行器偏航角模式
        SubElement(
            waypointHeadingParam, "wpml:waypointHeadingAngle"
        ).text = "0"  # 飞行器偏航角度
        SubElement(
            waypointHeadingParam, "wpml:waypointPoiPoint"
        ).text = "0.000000,0.000000,0.000000"  # 兴趣点
        SubElement(
            waypointHeadingParam, "wpml:waypointHeadingPathMode"
        ).text = "followBadArc"  # 飞行器偏航角转动方向
        SubElement(
            waypointHeadingParam, "wpml:waypointHeadingPoiIndex"
        ).text = "0"  # 飞行器偏航角转动方向

        waypointTurnParam = SubElement(Placemark, "wpml:waypointTurnParam")
        SubElement(
            waypointTurnParam, "wpml:waypointTurnMode"
        ).text = "toPointAndStopWithDiscontinuityCurvature"  # 全局航点类型（全局航点转弯模式）
        SubElement(
            waypointTurnParam, "wpml:waypointTurnDampingDist"
        ).text = (
            "0.2"  # 全局航点类型（全局航点转弯模式）todo  两文件不太一致  后续查找原因
        )
        SubElement(Placemark, "wpml:useStraightLine").text = "1"  # 该航段是否贴合直线
        SubElement(Placemark, "wpml:isRisky").text = "0"  # 是否危险点
        SubElement(Placemark, "wpml:waypointWorkType").text = "0"

    # 生成格式化的XML
    xml_str = minidom.parseString(tostring(kml)).toprettyxml(indent="  ")
    return xml_str


def write_xml(input_file, xml_str, xml_type):
    # 写入KML文件
    try:
        base_path = os.getcwd()
        workdir = os.path.join(base_path, "output")
        os.makedirs(workdir, exist_ok=True)

        basename = os.path.splitext(os.path.basename(input_file))[0]
        new_dir = os.path.join(workdir, basename)
        wpmz_dir = os.path.join(new_dir, "wpmz")
        os.makedirs(new_dir, exist_ok=True)
        os.makedirs(wpmz_dir, exist_ok=True)
        output_file = os.path.join(wpmz_dir, xml_type)
        with open(output_file, "w", encoding="utf-8") as f:
            f.write(xml_str)
        # print(f"转换成功! 已生成 {output_file}")
    except Exception as e:
        print(f"写入KML文件失败: {e}")


def zip_directory(input_file):
    """
    将指定文件夹压缩为 ZIP 文件

    参数:
        folder_path (str): 要压缩的文件夹路径
        output_zip (str): 输出的 ZIP 文件路径
    """
    # 确保输出目录存在
    base_path = os.getcwd()
    output_dir = os.path.join(base_path, "output")
    output_zip_dir = os.path.join(base_path, "output_zip")
    basename = os.path.splitext(os.path.basename(input_file))[0]
    folder_path = os.path.join(output_dir, basename)
    output_file = os.path.join(output_zip_dir, basename.replace("_", "") + ".kmz")
    os.makedirs(output_zip_dir, exist_ok=True)

    with zipfile.ZipFile(output_file, "w", zipfile.ZIP_DEFLATED) as zipf:
        # 遍历文件夹中的所有文件和子目录
        for root, dirs, files in os.walk(folder_path):
            for file in files:
                # 获取文件完整路径
                file_path = os.path.join(root, file)

                # 计算 ZIP 中的相对路径
                rel_path = os.path.relpath(file_path, folder_path)

                # 将文件添加到 ZIP
                zipf.write(file_path, rel_path)

            # 添加空文件夹 (可选)
            for dir_name in dirs:
                dir_path = os.path.join(root, dir_name)
                rel_path = os.path.relpath(dir_path, folder_path) + "/"
                if not os.listdir(dir_path):  # 检查是否为空文件夹
                    zipf.writestr(rel_path, b"")  # 添加空目录标记
    print(f"转换成功! 已生成 {output_file}")


def geod_EGM96():
    # 初始化Geod对象，这里不需要特别的参数因为我们要用的是默认的WGS84大地水准面
    geod = Geod(ellps="WGS84")

    # 定义WGS84坐标（经度，纬度，大地高）
    lon, lat, height_wgs84 = (
        -122.0,
        47.0,
        0.0,
    )  # 示例坐标，高度设为0（因为没有初始大地高）

    # 使用geod.geoid_height()计算大地高差（相对于EGM96的）
    geoid_height = geod.geoid_height(lon, lat)

    # 计算EGM96大地高（WGS84高度 + 地理高差）
    height_egm96 = height_wgs84 + geoid_height

    print(f"EGM96大地高: {height_egm96}")


def json_to_kmz(uav_data: dict) -> bytes:
    """
    将无人机路径数据转换为KMZ二进制数据
    :param uav_data: 无人机路径数据字典
    :return: KMZ文件的二进制数据
    """
    # 生成KML和WPML内容
    kml_str = json_to_kml(uav_data)
    wpml_str = json_to_wpml(uav_data)

    # 在内存中创建ZIP文件
    memory_zip = io.BytesIO()

    with zipfile.ZipFile(memory_zip, "w", zipfile.ZIP_DEFLATED) as zipf:
        # 创建wpmz目录结构并添加文件
        zipf.writestr("wpmz/template.kml", kml_str.encode("utf-8"))
        zipf.writestr("wpmz/waylines.wpml", wpml_str.encode("utf-8"))

    # 获取二进制数据
    memory_zip.seek(0)
    kmz_data = memory_zip.getvalue()
    memory_zip.close()

    return kmz_data


def main(input_path):
    for root, dirs, files in os.walk(input_path):
        for file in files:
            suffix = os.path.splitext(os.path.basename(file))[-1]
            if suffix != ".json":
                continue
            # 获取文件完整路径
            file_path = os.path.join(root, file)

            uav_data = read_json(file_path)
            kml_str = json_to_kml(uav_data)
            write_xml(file_path, kml_str, "template.kml")
            wpml_str = json_to_wpml(uav_data)
            write_xml(file_path, wpml_str, "waylines.wpml")
            zip_directory(file_path)


if __name__ == "__main__":
    # 设置命令行参数
    parser = argparse.ArgumentParser(description="将JSON文件转换为KML格式")
    parser.add_argument("--input", help="输入JSON文件路径")
    # parser.add_argument('output', help='输出KML文件路径')
    args = parser.parse_args()
    # 执行转换
    main(args.input)
