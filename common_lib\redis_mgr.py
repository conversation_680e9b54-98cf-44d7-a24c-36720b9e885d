# -*- coding: utf-8 -*-
""" Redis连接使用函数库
python 版本：python3
Name：redis_mgr.py
Description:Redis连接使用函数库
Author:胡旭
Version:1.0
Date:2022/11/07
Redis是用C语言开发的一个开源的高性能键值对（key-value）数据库，官方提供测试数
据，50个并发执行100000个请求，读的速度是110000次/秒，写的速度是81000次/秒，
且Redis通过提供多种键值数据类型来适应不同场景下的存储需求，目前为止Redis支持的
键值数据类型，如有
字符串类型（String）
哈希类型（hash）
列表类型（list）
集合类型（set）
有序集合类型（SortedSet）
"""
import redis
from loguru import logger
from redis.sentinel import Sentinel


class RedisMgr():
    """ Redis高速缓存数据库连接类
    """

    def __init__(self, redis_conf):
        """
        初始化 Redis 连接。

        根据传入的 Redis 配置，初始化 Redis 连接。支持两种模式：
        1. Sentinel 模式：当配置中包含哨兵信息时，使用哨兵模式连接 Redis。
        2. 单节点模式：当配置中不包含哨兵信息时，直接连接 Redis 单节点。

        参数:
        redis_conf (dict): Redis 配置信息，包含连接参数。具体结构如下：
            - connect (dict): 连接配置，包含以下字段：
                - sentinels (dict, 可选): 哨兵配置，包含以下字段：
                    - hosts (str): 哨兵节点地址列表，格式为 "host1:port1,host2:port2,..."。
                    - master (str): 主节点名称。
                - hosts (str): Redis 单节点地址。
                - port (int): Redis 单节点端口。
                - password (str, 可选): Redis 认证密码。
                - db (int, 可选): Redis 数据库编号。
        """
        self.sentinels_address = []
        self.is_sentinels = False
        self.conf = redis_conf
        redis_conn = redis_conf["connect"]

        # 检查是否为哨兵模式
        if "sentinels" in redis_conn and "hosts" in redis_conn["sentinels"]:
            # 解析哨兵节点地址
            self.sentinels_address = self.parse_parameter(redis_conn["sentinels"]["hosts"])
            self.master = redis_conn["sentinels"]["master"]
            # 初始化哨兵连接
            self.sentinels = Sentinel(self.sentinels_address, socket_timeout=0.5)
            self.is_sentinels = True
        else:
            # 单节点模式，初始化连接池
            self.redis_connect_pool = redis.ConnectionPool(
                host=redis_conf["connect"]["hosts"],
                port=redis_conf["connect"]["port"],
                password=redis_conf["connect"]["password"],
                db=redis_conf["connect"]["db"]
            )
            # 初始化 Redis 连接管理器
            self.redis_mgr = redis.Redis(connection_pool=self.redis_connect_pool)

    def parse_parameter(self, parameters: list) -> list:
        """ 解析参数列表

        该函数接收一个参数列表，每个参数是一个字符串，格式为"key:value"。
        函数会将每个字符串解析为(key, value)的元组，并将value转换为整数类型。
        最后返回一个包含所有解析后元组的列表。

        Args:
            parameters (list): 参数列表，每个元素是一个字符串，格式为"key:value"。

        Returns:
            list: 解析后的参数列表，每个元素是一个(key, value)的元组，其中value为整数类型。
        """
        pars_list = []
        # 遍历参数列表，将每个参数解析为(key, value)的元组，并将value转换为整数
        for parameter in parameters:
            res = parameter.split(":", 1)
            pars_list.append((res[0], int(res[1])))
        return pars_list

    @logger.catch
    def get_master_connect(self):
        """ 
        建立一个Redis客户端连接并存入self.redis_mgr。

        该方法通过配置信息中的数据库和密码，使用Redis哨兵模式连接到主节点，
        并将连接对象存储在self.redis_mgr中。如果连接过程中发生ValueError异常，
        会记录错误日志。

        参数:
            self: 类的实例对象，包含配置信息和Redis连接管理器。

        返回值:
            无
        """
        try:
            # 从配置中获取数据库和密码信息
            database = self.conf["connect"]["db"]
            pwd = self.conf["connect"]["password"]

            # 使用哨兵模式连接到Redis主节点，并将连接对象存入self.redis_mgr
            self.redis_mgr = self.sentinels.master_for(
                self.master, socket_timeout=0.5, db=database, password=pwd)
        except ValueError:
            # 如果连接过程中发生ValueError异常，记录错误日志
            logger.error("Redis sentinels connect error")

    @logger.catch
    def save_hash_data_to_redis(self, map_data):
        """
        将字典数据保存到 Redis 的哈希结构中。

        参数:
        - self: 类的实例对象，用于访问类中的其他方法或属性。
        - map_data: 字典类型，包含要保存到 Redis 的键值对数据。

        返回值:
        无返回值。该函数仅负责将数据写入 Redis，不返回任何结果。
        """
        # 遍历字典中的每个键值对，并将其写入 Redis 的哈希结构中
        for key in map_data:
            self.write_to_redis_hash(key, map_data[key])

    @logger.catch
    def save_mset_data_to_redis(self, map_data):
        """
        将字典数据批量写入Redis。

        参数:
        - map_data (dict): 需要写入Redis的键值对字典。键为Redis中的键名，值为对应的值。

        返回值:
        - 无返回值。如果字典非空，则调用`msets`方法将数据批量写入Redis。
        """
        # 检查字典是否非空，避免不必要的操作
        if len(map_data):
            # 调用`msets`方法将字典数据批量写入Redis
            self.msets(map_data)

    @logger.catch
    def write_to_redis_hash(self, key, value):
        """
        将给定的键值对写入 Redis 哈希表中。

        参数:
        - key (str): Redis 哈希表的键。
        - value (dict): 要写入哈希表的值，通常是一个字典。

        返回值:
        - 无

        说明:
        如果当前连接配置为哨兵模式（is_sentinels 为 True），则首先获取主节点的连接。
        然后使用 Redis 管理器的 hmset 方法将键值对写入 Redis 哈希表。
        """
        # 如果配置为哨兵模式，获取主节点连接
        if self.is_sentinels:
            self.get_master_connect()
        
        # 将键值对写入 Redis 哈希表
        self.redis_mgr.hmset(key, value)

    @logger.catch
    def write_to_redis_str(self, key:str, value):
        """向redis存一条缓存信息

        该函数用于将指定的键值对存储到Redis中。如果当前连接配置为哨兵模式，函数会首先获取主节点的连接，
        然后使用Redis管理器将键值对存储到Redis中。

        Args:
            key (str): 要存储的键，类型为字符串。
            value (_Value): 要存储的值，类型可以是任意Redis支持的数据类型。

        Returns:
            None: 该函数没有返回值。
        """
        # 如果当前连接配置为哨兵模式，获取主节点的连接
        if self.is_sentinels:
            self.get_master_connect()
        
        # 使用Redis管理器将键值对存储到Redis中
        self.redis_mgr.set(key, value)

    @logger.catch
    def keys(self, pattens)->list:
        """获取指定pattens的键值列表

        该方法用于从Redis中获取与指定模式匹配的键值列表。如果当前连接是哨兵模式，
        则会先获取主节点的连接。

        Args:
            pattens (str): 用于匹配键的模式字符串，支持通配符。

        Returns:
            list: 返回与指定模式匹配的键值列表，列表中的键会被转换为字符串格式。
        """
        # 如果当前连接是哨兵模式，获取主节点连接
        if self.is_sentinels:
            self.get_master_connect()
        
        # 从Redis中获取与指定模式匹配的键值列表
        res = self.redis_mgr.keys(pattens)
        
        # 将结果中的键转换为字符串格式
        self.to_str(res)
        
        return res

    @logger.catch
    def mgets(self, keys:list)->list:
        """ 批量读取缓存数据

        Args:
            keys (list): 键值列表，包含需要从缓存中获取数据的键。

        Returns:
            list: 数值列表，包含与输入键对应的缓存数据。如果某个键不存在，则对应的值为None。
        """
        # 如果当前连接是哨兵模式，则获取主节点连接
        if self.is_sentinels:
            self.get_master_connect()
        
        # 使用Redis的mget命令批量获取键对应的值
        values = self.redis_mgr.mget(keys)
        
        # 将获取到的值转换为字符串格式
        self.to_str(values)
        
        return values

    @logger.catch
    def msets(self, keysm):
        """ 批量写入redis

        该函数用于将一组键值对批量写入Redis。如果当前连接是哨兵模式，会先获取主节点连接，然后调用Redis管理器的mset方法进行批量写入。

        Args:
            keysm (Mapping[_Key, _Value]): 待写入的键值对列表，其中_Key表示键的类型，_Value表示值的类型。

        Returns:
            bool: 写入是否成功，成功返回True，失败返回False。
        """
        # 如果当前连接是哨兵模式，获取主节点连接
        if self.is_sentinels:
            self.get_master_connect()
        
        # 调用Redis管理器的mset方法进行批量写入，并返回写入结果
        return self.redis_mgr.mset(keysm)

    @logger.catch
    def delkeys(self, keys:list)->bool:
        """ 批量删除键值

        该函数用于批量删除Redis中的键值。如果键值列表为空，则直接返回0。如果当前连接是哨兵模式，
        则首先获取主节点的连接。最后调用Redis管理器的delete方法删除指定的键值。

        Args:
            keys (list): 需要删除的键值列表。列表中的每个元素应为字符串类型的键名。

        Returns:
            bool: 删除操作是否成功。成功返回True，失败返回False。
        """
        # 如果键值列表为空，直接返回0
        if len(keys) == 0:
            return 0
        
        # 如果当前连接是哨兵模式，获取主节点的连接
        if self.is_sentinels:
            self.get_master_connect()
        
        # 调用Redis管理器的delete方法删除指定的键值，并返回删除结果
        return self.redis_mgr.delete(keys)

    def to_str(self, values:list):
        """ 将redis返回的bytes类型解码为字符串

        该函数遍历传入的列表，将其中每个非None的bytes类型元素解码为字符串。
        解码过程中，如果遇到无法解码的字符，会使用替换字符（如'?'）进行替换。

        Args:
            values (list): 包含bytes类型元素的列表，通常是从Redis返回的数据。

        Returns:
            None: 该函数直接修改传入的列表，不返回任何值。
        """
        # 遍历列表，解码每个非None的bytes类型元素
        for ind,value in enumerate(values):
            if value is not None:
                # 使用'replace'错误处理策略解码bytes为字符串
                values[ind] = value.decode(errors='replace')

    def expire_key(self, key: str, timeout: int) -> bool:
        """ 设置键的过期时间

        Args:
            key (str): 需要设置过期时间的键名。
            timeout (int): 过期时间，单位为秒。

        Returns:
            bool: 如果设置成功，返回 True；否则返回 False。

        Notes:
            如果当前连接是哨兵模式，会先获取主节点的连接。
        """
        # 如果当前连接是哨兵模式，获取主节点的连接
        if self.is_sentinels:
            self.get_master_connect()
        
        # 调用 Redis 管理器的 expire 方法设置键的过期时间
        return self.redis_mgr.expire(key, timeout)

    def set(self, key: str, value):
        """ 设置键值对

        Args:
            key (str): 需要设置的键，类型为字符串。
            value: 需要设置的值，类型不限。

        功能描述:
            该方法用于在Redis中设置一个键值对。如果当前连接是哨兵模式（is_sentinels为True），
            则会先获取主节点的连接，然后再进行设置操作。

        重要代码块说明:
            - `if self.is_sentinels:` 检查当前是否为哨兵模式，如果是则获取主节点连接。
            - `self.redis_mgr.set(key, value)` 调用Redis管理器的set方法，将键值对存储到Redis中。
        """
        if self.is_sentinels:
            self.get_master_connect()
        self.redis_mgr.set(key, value)

    def get(self, key: str):
        """ 获取键对应的值

        Args:
            key (str): 键

        Returns:
            value: 键对应的值，如果键不存在，返回 None。
        """
        # 如果当前连接是哨兵模式，获取主节点连接
        if self.is_sentinels:
            self.get_master_connect()
        
        # 从 Redis 管理器中获取键对应的值
        value = self.redis_mgr.get(key)
        
        # 如果值存在，则返回该值；否则返回 None
        if value is not None:
            return value
        return None

    def set_with_expire(self, key: str, value, expire: int):
        """ 设置键值对并指定过期时间

        该函数用于在Redis中设置一个键值对，并为该键设置一个过期时间。如果当前连接是哨兵模式，
        则会先获取主节点的连接，然后再执行设置操作。

        Args:
            key (str): 要设置的键，类型为字符串。
            value: 要设置的值，可以是任意类型。
            expire (int): 键的过期时间，单位为秒。

        Returns:
            None: 该函数没有返回值。
        """
        # 如果当前连接是哨兵模式，则获取主节点的连接
        if self.is_sentinels:
            self.get_master_connect()
        
        # 使用Redis管理器设置键值对并指定过期时间
        self.redis_mgr.setex(key, expire, value)


    def get_expire(self, key: str) -> int:
        """ 获取键的剩余过期时间

        Args:
            key (str): 需要查询剩余过期时间的键名。

        Returns:
            int: 键的剩余过期时间，单位为秒。如果键不存在或没有设置过期时间，返回 -1。
        """
        # 如果当前连接是哨兵模式，则获取主节点的连接
        if self.is_sentinels:
            self.get_master_connect()
        
        # 返回键的剩余过期时间
        return self.redis_mgr.ttl(key)
    
    def check_exist(self, key: str) -> bool:
        """ 检查键是否存在

        Args:
            key (str): 需要检查的键名。

        Returns:
            bool: 如果键存在，返回 True；否则返回 False。
        """
        # 如果当前连接是哨兵模式，获取主节点连接
        if self.is_sentinels:
            self.get_master_connect()
        
        # 检查键是否存在，并返回布尔值
        return self.redis_mgr.exists(key) > 0    
    
    def del_key(self, key: str) -> bool:
        """ 删除指定键并检查键是否存在

        Args:
            key (str): 需要删除的键名。

        Returns:
            bool: 如果键存在并成功删除，返回 True；否则返回 False。
        """
        # 如果当前连接是哨兵模式，获取主节点连接
        if self.is_sentinels:
            self.get_master_connect()

        # 检查键是否存在，如果存在则删除并返回删除结果
        if self.redis_mgr.exists(key):
            return self.redis_mgr.delete(key) > 0
        
        # 如果键不存在，返回 False
        return False
    