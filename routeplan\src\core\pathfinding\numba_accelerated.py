# -*- coding: utf-8 -*-
"""
Numba加速的数值计算函数
这些函数从jps_v4.py中提取出来，使用Numba进行JIT编译加速
"""

import numpy as np
import numba
from numba import jit, types
from numba.typed import List
import math
from typing import Tuple


@jit(nopython=True, cache=True)
def manhattan_distance_numba(pos1: Tuple[float, float, float], pos2: Tuple[float, float, float]) -> float:
    """使用Numba加速的曼哈顿距离计算
    
    Args:
        pos1, pos2: 两个位置坐标 (y, x, z)
    
    Returns:
        float: 两点间的曼哈顿距离
    """
    return abs(pos2[0] - pos1[0]) + abs(pos2[1] - pos1[1]) + abs(pos2[2] - pos1[2])


@jit(nopython=True, cache=True)
def octile_distance_numba(pos1: Tuple[float, float, float], pos2: <PERSON><PERSON>[float, float, float]) -> float:
    """使用Numba加速的八方向距离计算
    
    Args:
        pos1, pos2: 两个位置坐标 (y, x, z)
    
    Returns:
        float: 两点间的八方向距离
    """
    # 计算各个轴上的差距
    dy = abs(pos2[0] - pos1[0])
    dx = abs(pos2[1] - pos1[1]) 
    dz = abs(pos2[2] - pos1[2])
    
    # 计算xy平面上的对角移动和直线移动
    diag_xy = min(dx, dy)  # 对角移动次数
    straight_xy = max(dx, dy) - diag_xy  # 直线移动次数
    
    # 对角移动成本为1.414，直线移动成本为1
    # 加上z轴的移动成本
    return diag_xy * 1.414 + straight_xy + dz


@jit(nopython=True, cache=True)
def euclidean_distance_numba(pos1: Tuple[float, float, float], pos2: Tuple[float, float, float]) -> float:
    """使用Numba加速的欧几里得距离计算
    
    Args:
        pos1, pos2: 两个位置坐标 (y, x, z)
    
    Returns:
        float: 两点间的欧几里得距离
    """
    return math.sqrt(
        (pos2[0] - pos1[0]) ** 2 + 
        (pos2[1] - pos1[1]) ** 2 + 
        (pos2[2] - pos1[2]) ** 2
    )


@jit(nopython=True, cache=True)
def euclidean_distance_squared_numba(pos1: Tuple[float, float, float], pos2: Tuple[float, float, float]) -> float:
    """使用Numba加速的欧几里得距离平方计算（避免开方运算）
    
    Args:
        pos1, pos2: 两个位置坐标 (y, x, z)
    
    Returns:
        float: 两点间的欧几里得距离的平方
    """
    return (
        (pos1[0] - pos2[0]) ** 2 +
        (pos1[1] - pos2[1]) ** 2 +
        (pos1[2] - pos2[2]) ** 2
    )


@jit(nopython=True, cache=True)
def moving_average_weights_numba(window_size: int) -> np.ndarray:
    """使用Numba加速的高斯权重计算
    
    Args:
        window_size: 窗口大小
    
    Returns:
        np.ndarray: 归一化的高斯权重数组
    """
    weights = np.zeros(window_size, dtype=np.float64)
    center = window_size // 2
    
    for i in range(window_size):
        # 计算相对于中心的距离
        distance = abs(i - center) / center if center > 0 else 0.0
        # 高斯权重
        weights[i] = math.exp(-0.5 * distance * distance)
    
    # 归一化权重
    weight_sum = np.sum(weights)
    if weight_sum > 0:
        weights = weights / weight_sum
    
    return weights


@jit(nopython=True, cache=True)
def weighted_average_position_numba(
    positions: np.ndarray,  # shape: (n, 3) - n个(y,x,z)位置
    weights: np.ndarray     # shape: (n,) - n个权重
) -> Tuple[float, float, float]:
    """使用Numba加速的加权平均位置计算
    
    Args:
        positions: 位置数组，形状为(n, 3)
        weights: 权重数组，形状为(n,)
    
    Returns:
        Tuple[float, float, float]: 加权平均后的(y, x, z)位置
    """
    if positions.shape[0] == 0:
        return 0.0, 0.0, 0.0
    
    y_sum = 0.0
    x_sum = 0.0
    z_sum = 0.0
    
    for i in range(positions.shape[0]):
        y_sum += positions[i, 0] * weights[i]
        x_sum += positions[i, 1] * weights[i]
        z_sum += positions[i, 2] * weights[i] 
    
    return y_sum, x_sum, z_sum


# 预热Numba JIT编译器
def _warmup_numba_functions():
    """预热Numba函数，触发JIT编译以减少首次调用延迟"""
    try:
        # 使用简单的测试数据触发编译
        pos1 = (0.0, 0.0, 0.0)
        pos2 = (1.0, 1.0, 1.0)
        
        manhattan_distance_numba(pos1, pos2)
        octile_distance_numba(pos1, pos2)
        euclidean_distance_numba(pos1, pos2)
        euclidean_distance_squared_numba(pos1, pos2)
        
        weights = moving_average_weights_numba(5)
        positions = np.array([[0.0, 0.0, 0.0], [1.0, 1.0, 1.0]], dtype=np.float64)
        test_weights = np.array([0.5, 0.5], dtype=np.float64)
        weighted_average_position_numba(positions, test_weights)
        
    except Exception as e:
        print(f"Warning: Numba预热过程中出现错误: {e}")
        # 不要抛出异常，让程序继续运行


# 在模块导入时预热函数
_warmup_numba_functions()
