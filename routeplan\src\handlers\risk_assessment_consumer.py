from typing import List, Optional
import json
import time
import logging
import threading
import uuid
from termcolor import colored

# from pyproj import error
import redis

# from datetime import datetime
from kafka import KafkaConsumer, KafkaProducer
from kafka.admin import KafkaAdminClient, NewTopic
from kafka.errors import TopicAlreadyExistsError, NoBrokersAvailable
from ..config import settings  # Corrected import
from ..utils.logging import get_logger
from ..utils.exceptions import KafkaError
from ..utils.unified_timer_manager import UnifiedTimerManager
from .kafka_consumer import PathPlanningConsumer
from .parallel_message_processor import ParallelMessageProcessor

# from routeplan.tests import visualize_solution, Visualizer3D

logger = get_logger(__name__)


class RiskAssessmentConsumer:
    """风险评估Kafka消费者"""

    # 类变量，用于管理Redis连接
    redis_client = None
    redis_conn_last_active = None

    # 类变量，用于管理消费者
    state_machine_consumer = None  # 状态机消费者实例
    state_machine_consumer_thread = None  # 状态机消费者线程
    flight_monitor_consumer = None  # 飞行监控消费者（禁飞区和改航请求）
    flight_monitor_consumer_thread = None  # 飞行监控消费者线程

    # 统一定时器管理器
    unified_timer_manager = None

    # 统一定时器间隔（秒）- 所有定时任务使用相同的间隔
    timer_interval = 60

    def __init__(
        self,
        bootstrap_servers: Optional[List[str]] = None,
        request_topic: Optional[str] = None,
        flighttask_state_topic: Optional[str] = None,
        flighttask_method_topic: Optional[str] = None,
    ):
        """
        初始化风险评估消费者

        Args:
            bootstrap_servers: Kafka服务器地址列表
            request_topic: 请求主题（用于监听禁飞区消息）
            flighttask_state_topic: 任务状态主题
            flighttask_method_topic: 任务方法主题
        """
        # 初始化关闭标志
        self._closing = False
        self.bootstrap_servers = (
            bootstrap_servers or settings.settings.kafka.bootstrap_servers
        )  # Corrected access
        self.request_topic = (
            request_topic or settings.settings.kafka.request_topic
        )  # Corrected access
        self.uav_topic = settings.settings.kafka.uav_topic  # Corrected access
        self.no_fly_zone_and_flight_status = (
            settings.settings.kafka.no_fly_zone_and_flight_status  # Corrected access
        )

        self.flighttask_state_topic = (
            flighttask_state_topic
            or settings.settings.kafka.flighttask_state_topic  # Corrected access
        )
        self.flighttask_method_topic = (
            flighttask_method_topic
            or settings.settings.kafka.flighttask_method_topic  # Corrected access
        )

        # 初始化内部的PathPlanningConsumer（禁用Kafka初始化）
        self.path_planning = PathPlanningConsumer(
            bootstrap_servers=self.bootstrap_servers,
            init_kafka=False,  # 禁用Kafka初始化
        )

        # 初始化并行消息处理器
        self.parallel_processor = ParallelMessageProcessor(
            max_workers=settings.settings.parallel_processing.max_workers
        )

        # 初始化统一定时器管理器（单例模式）
        if RiskAssessmentConsumer.unified_timer_manager is None:
            RiskAssessmentConsumer.unified_timer_manager = UnifiedTimerManager(
                timer_interval=RiskAssessmentConsumer.timer_interval
            )
            logger.info(
                f"已初始化统一定时器管理器，间隔: {RiskAssessmentConsumer.timer_interval}秒"
            )

        # 初始化Redis连接
        self._init_redis_conn()

        # 初始化no_fly_zone_and_flight_status消费者
        self._init_kafka()

        # 初始化状态机消费者（专门处理flighttask_method_topic）
        self._init_state_machine_consumer()

        # 注册所有定时任务
        self._register_timer_tasks()

        # 启动状态机消费者线程
        self._start_state_machine_consumer_thread()

        # 启动飞行监控消费者线程
        self._start_flight_monitor_consumer_thread()

    def _ensure_topic_exists(
        self, topic_name: str, num_partitions: int = 1, replication_factor: int = 1
    ):
        """确保主题存在，如果不存在则创建"""
        try:
            admin_client = KafkaAdminClient(bootstrap_servers=self.bootstrap_servers)

            # 创建主题配置
            topic = NewTopic(
                name=topic_name,
                num_partitions=num_partitions,
                replication_factor=replication_factor,
            )

            # 尝试创建主题
            admin_client.create_topics([topic])
            logger.info(f"已创建主题: {topic_name}")
        except TopicAlreadyExistsError:
            logger.info(f"主题已存在: {topic_name}")
        except NoBrokersAvailable as e:
            logger.error(f"无法连接到Kafka服务器: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"创建主题时出错: {str(e)}")
            raise
        finally:
            if admin_client:
                admin_client.close()

    def _init_redis_conn(self):
        """初始化Redis连接"""
        try:
            if RiskAssessmentConsumer.redis_client is None:
                # 优化Redis连接配置，降低内存使用
                RiskAssessmentConsumer.redis_client = redis.Redis(
                    host=settings.settings.redis.host,
                    port=settings.settings.redis.port,
                    db=settings.settings.redis.db,
                    decode_responses=True,
                    socket_timeout=10,  # 增加超时时间，减少重连频率
                    socket_keepalive=True,  # 启用keepalive，保持连接活跃
                    retry_on_timeout=True,
                    health_check_interval=30,  # 每30秒进行健康检查
                    max_connections=2,  # 限制最大连接数
                )
                RiskAssessmentConsumer.redis_conn_last_active = time.time()
                logger.info("Redis连接初始化成功")
        except Exception as e:
            logger.error(f"Redis连接初始化失败: {str(e)}")
            RiskAssessmentConsumer.redis_client = None
            raise RuntimeError(f"Redis连接初始化失败: {str(e)}")

    def _check_redis_conn(self):
        """检查并维护Redis连接"""
        # 检查关闭标志，避免在关闭过程中进行连接检查
        if hasattr(self, "_closing") and self._closing:
            logger.debug("服务正在关闭，跳过Redis连接检查")
            return False

        # 如果连接不存在，尝试初始化
        if RiskAssessmentConsumer.redis_client is None:
            logger.debug("Redis连接不存在，尝试初始化")
            try:
                self._init_redis_conn()
                return bool(RiskAssessmentConsumer.redis_client)
            except Exception as e:
                logger.error(f"Redis连接初始化失败: {str(e)}")
                return False

        # 检查连接是否有效，使用health_check_interval已经配置了自动检查
        # 只在上次活跃时间超过30分钟时进行主动检查，减少不必要的网络操作
        current_time = time.time()
        if (
            RiskAssessmentConsumer.redis_conn_last_active is None
            or current_time - RiskAssessmentConsumer.redis_conn_last_active > 1800
        ):  # 30分钟
            try:
                # 使用ping测试连接
                RiskAssessmentConsumer.redis_client.ping()
                RiskAssessmentConsumer.redis_conn_last_active = current_time
                return True
            except Exception as e:
                logger.warning(f"Redis连接测试失败: {str(e)}")
                try:
                    RiskAssessmentConsumer.redis_client.close()
                except:
                    pass
                RiskAssessmentConsumer.redis_client = None
                try:
                    self._init_redis_conn()
                except Exception as init_error:
                    logger.error(f"Redis重新连接失败: {str(init_error)}")
                return bool(RiskAssessmentConsumer.redis_client)

        # 如果最近有活跃，直接返回成功
        return True

    def _register_timer_tasks(self):
        """注册所有定时任务到统一定时器管理器"""
        # 注册Redis连接检查任务
        RiskAssessmentConsumer.unified_timer_manager.register_task(
            "redis_conn_check", self._check_redis_conn
        )
        logger.info("已注册Redis连接检查任务")

        # 注册路径清理任务
        RiskAssessmentConsumer.unified_timer_manager.register_task(
            "path_cleanup", self._cleanup_completed_flights
        )
        logger.info("已注册路径清理任务")

        # 注册Kafka心跳检查任务
        RiskAssessmentConsumer.unified_timer_manager.register_task(
            "kafka_heartbeat", self._check_kafka_connection
        )
        logger.info("已注册Kafka心跳检查任务")

    def _cleanup_completed_flights(self):
        """清理已完成的航班路径"""
        try:
            # 获取当前时间
            current_time = int(time.time())
            handler = self.path_planning.handlers["state"]

            # 需要删除的航班列表
            flights_to_remove = []

            # 遍历所有路径
            for flight_id, flight_info in handler.planned_paths.items():
                # 检查是否有降落时间
                if "turn_path_nodes" in flight_info:
                    landing_time = flight_info["turn_path_nodes"][-1].t
                    device_sn = flight_info["data"]["device_sn"]
                    try:
                        # 如果降落时间已过，添加到删除列表
                        if landing_time < current_time:
                            flights_to_remove.append(flight_id)
                            landing_time_str = time.strftime(
                                "%Y-%m-%d %H:%M:%S", time.localtime(landing_time)
                            )
                            logger.info(
                                f"航班 {device_sn} 的降落时间 {landing_time_str} 已过，将清理路径"
                            )
                    except Exception as e:
                        logger.warning(f"解析航班 {device_sn} 的降落时间失败: {str(e)}")

            # 删除过期航班
            for flight_id in flights_to_remove:
                # 从占用图中删除agent
                handler.occupancy_map.remove_agent(flight_id)

                # 从planned_paths中删除flight_id对应的路径
                if flight_id in handler.planned_paths:
                    del handler.planned_paths[flight_id]
                    # logger.info(
                    #     f"已删除内存中planned_paths中航班 {flight_id} 的路径信息（降落时间已过）"
                    # )

            # 只在有航班被清理时记录日志，减少日志量
            if flights_to_remove:
                # 获取禁飞区信息
                no_fly_zones = handler.map.obstacle_manager.get_all_types()
                if no_fly_zones:
                    logger.info(
                        f"定期清理已完成，共清理 {len(flights_to_remove)} 个已降落航班的路径， 还剩余 {len(handler.planned_paths)} 个航班路径。存在禁飞区：{list(no_fly_zones.keys())}"
                    )
                else:
                    logger.info(
                        f"定期清理已完成，共清理 {len(flights_to_remove)} 个已降落航班的路径， 还剩余 {len(handler.planned_paths)} 个航班路径。系统中不存在禁飞区"
                    )

            # # 每10次清理才记录一次无变化的日志，减少日志量
            elif hasattr(self, "_cleanup_count") and self._cleanup_count % 10 == 0:
                # 获取禁飞区信息
                no_fly_zones = handler.map.obstacle_manager.get_all_types()
                if no_fly_zones:
                    logger.info(
                        f"定期清理已完成，没有发现需要清理的航班，还剩余 {len(handler.planned_paths)} 个航班路径。存在禁飞区：{list(no_fly_zones.keys())}"
                    )
                else:
                    logger.info(
                        f"定期清理已完成，没有发现需要清理的航班，还剩余 {len(handler.planned_paths)} 个航班路径。系统中不存在禁飞区"
                    )

            # 更新清理计数
            if not hasattr(self, "_cleanup_count"):
                self._cleanup_count = 1
            else:
                self._cleanup_count += 1

        except Exception as e:
            logger.error(f"清理已完成航班路径时出错: {str(e)}")

    # 已移除 _start_kafka_heartbeat_timer 方法，功能整合到 _register_timer_tasks 方法中

    def _init_state_machine_consumer(self):
        """初始化状态机消费者（专门处理flighttask_method_topic）和状态机消息生产者"""
        # 设置重试参数
        max_retries = 5
        retry_interval = 2  # 初始重试间隔（秒）
        retry_count = 0

        while retry_count < max_retries:
            try:
                # 初始化状态机消费者
                if RiskAssessmentConsumer.state_machine_consumer is None:
                    logger.info(
                        f"尝试初始化状态机消费者 (尝试 {retry_count + 1}/{max_retries})"
                    )
                    RiskAssessmentConsumer.state_machine_consumer = KafkaConsumer(
                        self.flighttask_method_topic,  # 只监听状态机消息主题
                        bootstrap_servers=self.bootstrap_servers,
                        value_deserializer=lambda x: json.loads(x.decode("utf-8")),
                        auto_offset_reset="latest",
                        group_id=f"{settings.settings.kafka.group_id}-state-machine",  # 使用不同的消费者组 # Corrected access
                        # 批量处理配置 - 优化内存使用
                        fetch_max_wait_ms=150,  # 增加等待时间到150ms，减少轮询频率
                        fetch_min_bytes=1024,  # 等待至少1KB数据再返回，减少小批量处理
                        max_poll_records=20,  # 减少每次轮询的记录数，降低内存使用
                        # 心跳设置 - 优化心跳配置，减少会话过期问题
                        session_timeout_ms=60000,  # 60秒会话超时（增加超时时间）
                        heartbeat_interval_ms=15000,  # 15秒心跳间隔（保持在session_timeout_ms的1/4左右）
                        max_poll_interval_ms=1800000,  # 30分钟最大轮询间隔（增加轮询间隔）
                        # 连接设置
                        reconnect_backoff_ms=1000,  # 重连回退时间
                        reconnect_backoff_max_ms=10000,  # 最大重连回退时间
                        retry_backoff_ms=500,  # 重试回退时间
                        # 自动提交设置
                        enable_auto_commit=True,
                        auto_commit_interval_ms=5000,  # 每5秒自动提交（减少提交频率）
                        # 请求超时设置
                        request_timeout_ms=65000,  # 请求超时时间（必须大于session_timeout_ms）
                    )
                    logger.info(
                        "状态机消费者初始化成功，监听主题: "
                        + self.flighttask_method_topic
                    )

                # 初始化Kafka生产者（用于发送状态机相关消息）
                logger.info(
                    f"尝试初始化Kafka生产者 (尝试 {retry_count + 1}/{max_retries})"
                )
                self.producer = KafkaProducer(
                    bootstrap_servers=self.bootstrap_servers,
                    value_serializer=lambda x: json.dumps(x).encode("utf-8"),
                    # 连接设置
                    reconnect_backoff_ms=1000,  # 重连回退时间
                    reconnect_backoff_max_ms=10000,  # 最大重连回退时间
                    retry_backoff_ms=500,  # 重试回退时间
                    # 请求超时设置
                    request_timeout_ms=30000,  # 请求超时时间
                    # 批处理设置 - 优化内存使用
                    batch_size=8192,  # 减小批处理大小，降低内存使用
                    linger_ms=10,  # 增加等待时间，减少小批量发送
                    buffer_memory=16777216,  # 减小缓冲区内存大小（16MB）
                    # 压缩设置
                    compression_type="gzip",  # 使用gzip压缩
                    # 连接超时设置
                    connections_max_idle_ms=540000,  # 连接最大空闲时间（9分钟）
                    # 安全设置
                    security_protocol="PLAINTEXT",  # 使用明文协议
                    # 重试设置
                    retries=3,  # 发送失败时重试3次
                )

                # 测试生产者连接
                logger.info("测试Kafka生产者连接...")
                try:
                    self.producer.metrics()  # 获取指标以测试连接
                    logger.info("Kafka生产者连接测试成功")
                except Exception as producer_error:
                    logger.warning(f"Kafka生产者连接测试失败: {str(producer_error)}")
                    if "NoBrokersAvailable" in str(producer_error):
                        raise Exception(f"没有可用的Kafka代理: {str(producer_error)}")

                # 如果一切正常，返回成功
                logger.info("状态机消费者和生产者初始化成功")
                return True

            except Exception as e:
                retry_count += 1
                logger.error(
                    f"状态机消费者或生产者初始化失败 (尝试 {retry_count}/{max_retries}): {str(e)}"
                )

                # 清理可能部分初始化的资源
                if RiskAssessmentConsumer.state_machine_consumer is not None:
                    try:
                        RiskAssessmentConsumer.state_machine_consumer.close()
                    except:
                        pass
                RiskAssessmentConsumer.state_machine_consumer = None

                if hasattr(self, "producer") and self.producer is not None:
                    try:
                        self.producer.close()
                    except:
                        pass
                    self.producer = None

                # 如果是最后一次尝试，则记录错误并返回
                if retry_count >= max_retries:
                    logger.critical(
                        f"状态机消费者和生产者初始化失败，已达到最大重试次数 ({max_retries})"
                    )
                    return False

                # 计算等待时间（指数退避）
                wait_time = retry_interval * (2 ** (retry_count - 1))
                logger.info(f"{wait_time}秒后重试...")
                time.sleep(wait_time)

    def _start_state_machine_consumer_thread(self):
        """启动状态机消费者线程"""
        if RiskAssessmentConsumer.state_machine_consumer is None:
            logger.warning("状态机消费者不存在，无法启动线程")
            return

        if (
            RiskAssessmentConsumer.state_machine_consumer_thread is not None
            and RiskAssessmentConsumer.state_machine_consumer_thread.is_alive()
        ):
            logger.info("状态机消费者线程已经运行中")
            return

        # 创建新线程
        RiskAssessmentConsumer.state_machine_consumer_thread = threading.Thread(
            target=self._consume_state_machine_messages,
            name="StateMachineConsumerThread",
        )
        RiskAssessmentConsumer.state_machine_consumer_thread.daemon = (
            True  # 设置为守护线程
        )
        RiskAssessmentConsumer.state_machine_consumer_thread.start()
        logger.info("状态机消费者线程已启动")

    def _consume_state_machine_messages(self):
        """处理flighttask_method_topic消息的线程函数"""
        logger.info("开始监听状态机消息...")
        consecutive_errors = 0
        max_consecutive_errors = 5

        while True:
            # 检查关闭标志
            if hasattr(self, "_closing") and self._closing:
                logger.info("检测到关闭标志，状态机消费者线程正在退出...")
                break

            try:
                # 检查消费者是否存在，如果不存在则重新初始化
                if RiskAssessmentConsumer.state_machine_consumer is None:
                    # 检查是否正在关闭
                    if hasattr(self, "_closing") and self._closing:
                        logger.info("服务正在关闭，不再重新初始化状态机消费者")
                        break

                    logger.warning("状态机消费者不存在，尝试重新初始化")
                    self._init_state_machine_consumer()
                    if RiskAssessmentConsumer.state_machine_consumer is None:
                        logger.error("无法初始化状态机消费者，等待5秒后重试")
                        time.sleep(5)
                        continue

                # 使用轮询方式获取消息，现在可以一次获取多条消息
                start_poll_time = time.time()
                state_messages = RiskAssessmentConsumer.state_machine_consumer.poll(
                    timeout_ms=150,
                    max_records=20,  # 与max_poll_records配置保持一致，降低内存使用
                )
                poll_time = time.time() - start_poll_time

                # 如果没有消息，短暂休眠后继续下一轮轮询
                if not state_messages:
                    # 重置连续错误计数
                    consecutive_errors = 0
                    # 添加短暂休眠，减少CPU使用
                    time.sleep(0.1)
                    continue

                # 记录获取到的消息数量
                total_messages = sum(len(msgs) for msgs in state_messages.values())
                if total_messages > 1:
                    logger.info(
                        f"状态机消费者一次获取到 {total_messages} 条消息，轮询耗时: {poll_time:.3f}秒"
                    )

                # 批量处理开始时间
                batch_start_time = time.time()
                processed_count = 0
                error_count = 0

                # 计算消息总数
                total_messages_count = sum(
                    len(messages_list) for _, messages_list in state_messages.items()
                )

                # 从配置文件获取并行处理的阈值和启用状态
                parallel_enabled = settings.settings.parallel_processing.enabled
                parallel_threshold = (
                    settings.settings.parallel_processing.parallel_threshold
                )

                # 判断是否使用并行处理
                use_parallel = (
                    parallel_enabled and total_messages_count >= parallel_threshold
                )

                if use_parallel:
                    # 收集所有消息用于并行处理
                    all_messages = []
                    for _, messages_list in state_messages.items():
                        for message in messages_list:
                            all_messages.append(message.value)

                    # 使用并行处理函数处理所有消息
                    try:
                        logger.info(
                            f"消息数量({total_messages_count})超过阈值({parallel_threshold})，使用并行处理"
                        )
                        # 并行处理消息
                        results = self._process_messages_in_parallel(all_messages)

                        # 统计处理结果
                        processed_count = sum(
                            1 for r in results if r.get("success", False)
                        )
                        error_count = len(results) - processed_count

                        # 如果所有消息都处理成功，重置连续错误计数
                        if error_count == 0:
                            consecutive_errors = 0
                        else:
                            # 如果有错误，增加连续错误计数
                            consecutive_errors += 1

                            # 如果连续错误过多，重新初始化消费者
                            if consecutive_errors >= max_consecutive_errors:
                                logger.warning(
                                    f"连续 {consecutive_errors} 次处理错误，尝试重新初始化消费者"
                                )
                                try:
                                    if RiskAssessmentConsumer.state_machine_consumer:
                                        RiskAssessmentConsumer.state_machine_consumer.close()
                                    RiskAssessmentConsumer.state_machine_consumer = None
                                    self._init_state_machine_consumer()
                                    consecutive_errors = 0
                                except Exception as reinit_error:
                                    logger.error(
                                        f"重新初始化消费者失败: {str(reinit_error)}"
                                    )
                    except Exception as e:
                        logger.error(f"并行处理消息时出错: {str(e)}")
                        error_count = len(all_messages)
                        consecutive_errors += 1
                else:
                    # 使用原有的顺序处理方式
                    logger.info("使用顺序处理")
                    for _, messages_list in state_messages.items():
                        for message in messages_list:
                            try:
                                # 处理状态机消息
                                self._handle_state_machine_message(message.value)
                                # 成功处理后增加计数
                                processed_count += 1
                                # 重置连续错误计数
                                consecutive_errors = 0
                            except Exception as e:
                                logger.error(f"处理状态机消息时出错: {str(e)}")
                                error_count += 1
                                consecutive_errors += 1
                                # 如果连续错误过多，重新初始化消费者
                                if consecutive_errors >= max_consecutive_errors:
                                    logger.warning(
                                        f"连续 {consecutive_errors} 次处理错误，尝试重新初始化消费者"
                                    )
                                    try:
                                        if RiskAssessmentConsumer.state_machine_consumer:
                                            RiskAssessmentConsumer.state_machine_consumer.close()
                                        RiskAssessmentConsumer.state_machine_consumer = None
                                        self._init_state_machine_consumer()
                                        consecutive_errors = 0
                                    except Exception as reinit_error:
                                        logger.error(
                                            f"重新初始化消费者失败: {str(reinit_error)}"
                                        )
                                    break

                # 批量处理完成后提交偏移量
                try:
                    commit_start_time = time.time()
                    RiskAssessmentConsumer.state_machine_consumer.commit()
                    commit_time = time.time() - commit_start_time

                    # 记录批量处理性能指标
                    batch_time = time.time() - batch_start_time
                    if total_messages > 1:
                        logger.info(
                            f"状态机消费者批量处理完成: 总消息 {total_messages} 条，"
                            f"成功 {processed_count} 条，失败 {error_count} 条，"
                            f"处理耗时 {batch_time:.3f}秒，提交耗时 {commit_time:.3f}秒"
                        )
                except Exception as e:
                    logger.warning(f"状态机消费者提交偏移量失败: {str(e)}")
                    # 不因为提交失败就中断处理

            except Exception as e:
                # 检查关闭标志
                if hasattr(self, "_closing") and self._closing:
                    logger.info(f"服务正在关闭，状态机消费者线程退出: {str(e)}")
                    break

                logger.error(f"状态机消费者错误: {str(e)}")
                consecutive_errors += 1

                try:
                    # 关闭现有连接
                    if RiskAssessmentConsumer.state_machine_consumer:
                        RiskAssessmentConsumer.state_machine_consumer.close()
                    RiskAssessmentConsumer.state_machine_consumer = None

                    # 检查是否正在关闭
                    if hasattr(self, "_closing") and self._closing:
                        logger.info("服务正在关闭，不再重新初始化状态机消费者")
                        break

                    # 重新初始化
                    self._init_state_machine_consumer()
                except Exception as e2:
                    logger.error(f"状态机消费者重新连接失败: {str(e2)}")

                # 等待后重试，根据连续错误次数增加等待时间
                wait_time = min(5 * consecutive_errors, 30)  # 最多等待30秒
                logger.info(f"等待 {wait_time} 秒后重试连接")
                time.sleep(wait_time)

    def _handle_state_machine_message(self, message):
        """处理状态机消息"""
        try:
            source = message.get("source")
            desc = message.get("desc")

            # 根据消息类型进行处理
            if desc == "风险评估":
                logger.info(colored(f"收到状态机消息: {source} -> {desc}", "cyan"))
                self._handle_risk_assessment(message)
            elif desc in [
                "审批驳回",
                "审批通过",
                "待执行",
                "执行中",
                "结束-任务取消",
                "结束-执行异常",
            ]:
                logger.info(colored(f"收到状态机消息: {source} -> {desc}", "cyan"))
                self._handle_state_change(message)
            else:
                logger.debug(f"未处理的状态机消息类型: {desc}")

        except Exception as e:
            logger.error(f"处理状态机消息时出错: {str(e)}")

    def _check_kafka_connection(self):
        """检查Kafka连接状态"""
        # 检查当前线程状态
        try:
            thread_id = threading.get_ident()
            if thread_id is None:
                logger.warning(
                    "Kafka连接检查：当前线程ID为None，可能是线程已终止或未正确初始化"
                )
                # 如果系统正在关闭，直接返回
                if hasattr(self, "_closing") and self._closing:
                    logger.debug("服务正在关闭，跳过Kafka连接检查")
                    return
                # 使用一个固定值代替None
                thread_id = "unknown"
        except Exception as e:
            logger.warning(f"Kafka连接检查：获取线程ID时出错: {str(e)}")
            # 如果系统正在关闭，直接返回
            if hasattr(self, "_closing") and self._closing:
                logger.debug("服务正在关闭，跳过Kafka连接检查")
                return
            thread_id = "error"

        # 检查关闭标志
        if hasattr(self, "_closing") and self._closing:
            logger.debug("服务正在关闭，跳过Kafka连接检查")
            return

        try:
            # 检查飞行监控消费者
            flight_monitor_needs_reinit = False

            if (
                not hasattr(self, "flight_monitor_consumer")
                or self.flight_monitor_consumer is None
            ):
                flight_monitor_needs_reinit = True
                logger.warning(
                    f"[Thread-{thread_id}] 飞行监控消费者不存在，需要重新初始化"
                )
            else:
                # 检查心跳状态
                try:
                    # 检查协调器状态 - 使用更可靠的方法检查心跳状态
                    if hasattr(self.flight_monitor_consumer, "_coordinator"):
                        # 尝试获取消费者的成员资格，这会触发心跳
                        try:
                            # 使用更轻量的方法检查连接状态
                            self.flight_monitor_consumer.metrics()
                            # 如果没有异常，说明连接正常
                        except Exception as e:
                            logger.warning(
                                f"[Thread-{thread_id}] 飞行监控消费者连接检查失败: {str(e)}"
                            )
                            flight_monitor_needs_reinit = True
                except Exception as e:
                    if "Heartbeat failed" in str(
                        e
                    ) or "Coordinator not available" in str(e):
                        logger.warning(
                            f"[Thread-{thread_id}] 飞行监控消费者心跳检查失败，需要重新初始化: {str(e)}"
                        )
                        flight_monitor_needs_reinit = True
                    else:
                        logger.warning(
                            f"[Thread-{thread_id}] 飞行监控消费者检查失败: {str(e)}"
                        )

            # 如果需要重新初始化飞行监控消费者
            if flight_monitor_needs_reinit:
                # 再次检查关闭标志
                if hasattr(self, "_closing") and self._closing:
                    logger.debug(
                        f"[Thread-{thread_id}] 服务正在关闭，不再重新初始化飞行监控消费者"
                    )
                    return

                logger.warning(f"[Thread-{thread_id}] 正在重新初始化飞行监控消费者")
                try:
                    # 关闭现有连接
                    if (
                        hasattr(self, "flight_monitor_consumer")
                        and self.flight_monitor_consumer
                    ):
                        try:
                            self.flight_monitor_consumer.close(autocommit=False)
                        except Exception as close_error:
                            logger.warning(
                                f"[Thread-{thread_id}] 关闭飞行监控消费者失败: {str(close_error)}"
                            )
                    self.flight_monitor_consumer = None
                    # 重新初始化
                    self._init_kafka()
                except Exception as e2:
                    logger.error(
                        f"[Thread-{thread_id}] 飞行监控消费者重新初始化失败: {str(e2)}"
                    )

            # 检查状态机消费者
            state_machine_needs_reinit = False

            if RiskAssessmentConsumer.state_machine_consumer is None:
                state_machine_needs_reinit = True
                logger.warning(
                    f"[Thread-{thread_id}] 状态机消费者不存在，需要重新初始化"
                )
            else:
                # 检查心跳状态
                try:
                    # 检查协调器状态 - 使用更可靠的方法检查心跳状态
                    if hasattr(
                        RiskAssessmentConsumer.state_machine_consumer, "_coordinator"
                    ):
                        # 尝试获取消费者的成员资格，这会触发心跳
                        try:
                            # 使用更轻量的方法检查连接状态
                            RiskAssessmentConsumer.state_machine_consumer.metrics()
                            # 如果没有异常，说明连接正常
                        except Exception as e:
                            logger.warning(
                                f"[Thread-{thread_id}] 状态机消费者连接检查失败: {str(e)}"
                            )
                            state_machine_needs_reinit = True
                except Exception as e:
                    if "Heartbeat failed" in str(
                        e
                    ) or "Coordinator not available" in str(e):
                        logger.warning(
                            f"[Thread-{thread_id}] 状态机消费者心跳检查失败，需要重新初始化: {str(e)}"
                        )
                        state_machine_needs_reinit = True
                    else:
                        logger.warning(
                            f"[Thread-{thread_id}] 状态机消费者检查失败: {str(e)}"
                        )

            # 如果需要重新初始化状态机消费者
            if state_machine_needs_reinit:
                # 再次检查关闭标志
                if hasattr(self, "_closing") and self._closing:
                    logger.debug(
                        f"[Thread-{thread_id}] 服务正在关闭，不再重新初始化状态机消费者"
                    )
                    return

                logger.warning(f"[Thread-{thread_id}] 正在重新初始化状态机消费者")
                try:
                    # 关闭现有连接
                    if RiskAssessmentConsumer.state_machine_consumer:
                        try:
                            RiskAssessmentConsumer.state_machine_consumer.close(
                                autocommit=False
                            )
                        except Exception as close_error:
                            logger.warning(
                                f"[Thread-{thread_id}] 关闭状态机消费者失败: {str(close_error)}"
                            )
                    RiskAssessmentConsumer.state_machine_consumer = None
                    # 重新初始化
                    self._init_state_machine_consumer()
                    self._start_state_machine_consumer_thread()
                except Exception as e2:
                    logger.error(
                        f"[Thread-{thread_id}] 状态机消费者重新初始化失败: {str(e2)}"
                    )

            # 不记录成功的心跳检查日志，减少日志量
            # if not flight_monitor_needs_reinit and not state_machine_needs_reinit:
            #     logger.debug("Kafka连接心跳检查成功")

        except Exception as e:
            # 再次检查关闭标志
            if hasattr(self, "_closing") and self._closing:
                logger.debug(
                    f"[Thread-{thread_id}] 服务正在关闭，忽略Kafka连接错误: {str(e)}"
                )
                return

            logger.warning(
                f"[Thread-{thread_id}] Kafka连接心跳检查过程中出错: {str(e)}"
            )
            try:
                # 关闭现有连接
                if (
                    hasattr(self, "flight_monitor_consumer")
                    and self.flight_monitor_consumer
                ):
                    try:
                        self.flight_monitor_consumer.close(autocommit=False)
                    except Exception as close_error:
                        logger.warning(
                            f"[Thread-{thread_id}] 关闭飞行监控消费者失败: {str(close_error)}"
                        )
                self.flight_monitor_consumer = None

                # 重新初始化
                self._init_kafka()
            except Exception as e2:
                logger.error(f"[Thread-{thread_id}] Kafka重新连接失败: {str(e2)}")

    def _init_kafka(self):
        """初始化Kafka连接"""
        # 设置重试参数
        max_retries = 5
        retry_interval = 2  # 初始重试间隔（秒）
        retry_count = 0

        while retry_count < max_retries:
            try:
                # 启用详细日志
                logging.getLogger("kafka").setLevel(logging.WARNING)
                logging.getLogger("kafka.conn").setLevel(logging.WARNING)
                logging.getLogger("kafka.consumer").setLevel(logging.WARNING)
                logging.getLogger("kafka.coordinator").setLevel(logging.WARNING)

                # 记录当前尝试的服务器地址
                logger.info(
                    f"尝试连接Kafka服务器: {self.bootstrap_servers} (尝试 {retry_count + 1}/{max_retries})"
                )

                # 首先尝试创建一个简单的管理客户端来测试连接
                try:
                    admin_client = KafkaAdminClient(
                        bootstrap_servers=self.bootstrap_servers,
                        client_id="test_connection",
                        request_timeout_ms=5000,  # 5秒超时
                    )
                    # 尝试列出主题以验证连接
                    admin_client.list_topics()
                    admin_client.close()
                    logger.info("Kafka服务器连接测试成功")
                except Exception as admin_error:
                    logger.warning(f"Kafka服务器连接测试失败: {str(admin_error)}")
                    # 如果是NoBrokersAvailable错误，直接重试
                    if "NoBrokersAvailable" in str(admin_error):
                        retry_count += 1
                        wait_time = retry_interval * (
                            2 ** (retry_count - 1)
                        )  # 指数退避
                        logger.info(f"没有可用的Kafka代理，{wait_time}秒后重试...")
                        time.sleep(wait_time)
                        continue
                    # 其他错误也重试，但记录详细信息
                    else:
                        logger.error(f"Kafka管理客户端错误: {str(admin_error)}")
                        retry_count += 1
                        wait_time = retry_interval * (2 ** (retry_count - 1))
                        logger.info(f"{wait_time}秒后重试...")
                        time.sleep(wait_time)
                        continue

                # 初始化飞行监控消费者（禁飞区和改航请求）
                logger.info("初始化飞行监控消费者...")
                self.flight_monitor_consumer = KafkaConsumer(
                    self.no_fly_zone_and_flight_status,
                    bootstrap_servers=self.bootstrap_servers,
                    value_deserializer=lambda x: json.loads(x.decode("utf-8")),
                    auto_offset_reset="latest",
                    group_id=f"{settings.settings.kafka.group_id}-flight-monitor",  # 使用不同的消费者组
                    # 心跳设置 - 优化心跳配置，减少会话过期问题
                    session_timeout_ms=60000,  # 60秒会话超时
                    heartbeat_interval_ms=15000,  # 15秒心跳间隔
                    max_poll_interval_ms=1800000,  # 30分钟最大轮询间隔
                    # 批量处理配置 - 优化内存使用
                    fetch_max_wait_ms=100,  # 增加等待时间到100ms，减少轮询频率
                    fetch_min_bytes=1024,  # 等待至少1KB数据再返回，减少小批量处理
                    max_poll_records=20,  # 减少每次轮询的记录数，降低内存使用
                    # 连接设置
                    reconnect_backoff_ms=1000,  # 重连回退时间
                    reconnect_backoff_max_ms=10000,  # 最大重连回退时间
                    retry_backoff_ms=500,  # 重试回退时间
                    # 自动提交设置
                    enable_auto_commit=True,  # 启用自动提交
                    auto_commit_interval_ms=5000,  # 每5秒自动提交
                    # 请求超时设置
                    request_timeout_ms=65000,  # 请求超时时间（必须大于session_timeout_ms）
                    # 连接超时设置
                    connections_max_idle_ms=540000,  # 连接最大空闲时间（9分钟）
                    # 安全设置
                    security_protocol="PLAINTEXT",  # 使用明文协议
                    # 调试设置
                    api_version_auto_timeout_ms=10000,  # API版本自动检测超时
                )

                # 测试消费者连接
                logger.info("测试飞行监控消费者连接...")
                try:
                    self.flight_monitor_consumer.topics()  # 获取主题列表以测试连接
                    logger.info("飞行监控消费者连接测试成功")
                except Exception as consumer_error:
                    logger.warning(f"飞行监控消费者连接测试失败: {str(consumer_error)}")
                    if "NoBrokersAvailable" in str(consumer_error):
                        retry_count += 1
                        wait_time = retry_interval * (2 ** (retry_count - 1))
                        logger.info(f"没有可用的Kafka代理，{wait_time}秒后重试...")
                        time.sleep(wait_time)
                        continue

                # 注意：Kafka生产者现在在_init_state_machine_consumer函数中初始化

                logger.info("Kafka连接初始化成功")
                return True

            except Exception as e:
                retry_count += 1
                logger.error(
                    f"Kafka连接初始化失败 (尝试 {retry_count}/{max_retries}): {str(e)}"
                )

                # 如果是最后一次尝试，则抛出异常
                if retry_count >= max_retries:
                    logger.critical(
                        f"Kafka连接初始化失败，已达到最大重试次数 ({max_retries})"
                    )
                    raise KafkaError(f"Kafka连接初始化失败: {str(e)}")

                # 计算等待时间（指数退避）
                wait_time = retry_interval * (2 ** (retry_count - 1))
                logger.info(f"{wait_time}秒后重试...")
                time.sleep(wait_time)

    def _send_message(
        self,
        old_data: str,
        new_data: bool,
        # error: Optional[str] = None,
    ):
        """
        发送消息到flighttask_method主题

        Args:
            task_id: 任务ID
            state: 处理状态
            path_data: 路径数据（可选）
            reason: 错误原因（可选）
        """
        message = {
            "bid": str(uuid.uuid4()),
            "id": old_data["flightapplyid"],
            "module": "risk",
            "source": "风险评估",
            "timestamp": int(time.time()),
            "data": {},
        }

        if new_data["risk_state"]:
            message["desc"] = "流量评估"
        else:
            message["desc"] = "待调整"
        message["data"] = new_data

        self.producer.send(self.flighttask_method_topic, message)
        self.producer.flush()
        # logger.info(f"已发送消息到{self.flighttask_method_topic}: {message}")
        logger.info(
            colored(f"已发送消息到{self.flighttask_method_topic}: {message}", "green")
        )

    def _send_conflict_message(
        self,
        all_conflicts,
    ):
        """
        发送消息到flighttask_method主题

        Args:
            task_id: 任务ID
            state: 处理状态
            path_data: 路径数据（可选）
            reason: 错误原因（可选）
        """
        for conflict in all_conflicts:
            message = {
                "bid": str(uuid.uuid4()),
                "id": conflict["flight_id"],
                "module": "risk",
                "source": conflict["source"],
                "desc": "风险评估",
                "timestamp": int(time.time()),
                "data": {
                    "risk_state": False,
                    "risk_reason": "与新增的禁飞区发生冲突，需重新规划路径",
                },
            }

            self.producer.send(self.flighttask_method_topic, message)
            self.producer.flush()
            # logger.info(f"已发送消息到{self.flighttask_method_topic}: {message}")
            logger.info(
                colored(
                    f"已发送消息到{self.flighttask_method_topic}: {message}", "green"
                )
            )

    def _handle_reroute_request(self, request):
        """处理改航请求"""
        start_time = time.time()

        # 记录处理开始
        logger.debug(f"开始处理改航请求: {request.get('flight_id')}")

        handler_type = "reroute"
        handler = self.path_planning.handlers[handler_type]

        # request_alarm_index = request["posFinal"][1]
        flight_id = request["flight_id"]

        if flight_id not in handler.planned_paths:
            logger.error(f"告警处理时未找到飞行任务: {flight_id}")
            return
        # 判断request_alarm_index和self.path_planning.alarm_index相差是否小于5，则是重复告警，不处理
        # if (
        #     abs(request_alarm_index - handler.planned_paths[flight_id]["alarm_index"])
        #     < 5
        # ):
        #     logger.debug(f"忽略重复告警: {flight_id}, 告警索引差值小于5")
        #     return

        # 整理改航数据并发送第一次状态消息
        message_start = time.time()
        message = {
            "bid": str(uuid.uuid4()),
            "id": request["flight_id"],
            "module": "risk",
            "source": "执行中",
            "desc": "执行异常",
            "timestamp": int(time.time()),
            "data": request["data"],
        }
        self.producer.send(self.flighttask_method_topic, message)
        self.producer.flush()
        message_time = time.time() - message_start
        logger.debug(f"发送执行异常状态消息耗时: {message_time:.3f}秒")

        logger.info(
            colored(
                f"已发送执行异常状态到{self.flighttask_method_topic}: {message}",
                "green",
            )
        )

        # 计时处理消息的时间
        handle_start = time.time()
        new_path, _ = handler.handle_message(request)
        handle_time = time.time() - handle_start
        logger.debug(f"改航处理器处理消息耗时: {handle_time:.3f}秒")

        if new_path:
            # 发送状态机消息
            status_start = time.time()
            status_message = {
                "bid": str(uuid.uuid4()),
                "id": request["flight_id"],
                "module": "risk",
                "source": "执行异常",
                "desc": "执行中",
                "timestamp": int(time.time()),
                "data": {
                    # "new_path": new_path,
                    "risk_state": True,
                    "risk_reason": "改航成功",
                },
            }
            self.producer.send(self.flighttask_method_topic, status_message)
            self.producer.flush()
            status_time = time.time() - status_start
            logger.debug(f"发送改航结果消息耗时: {status_time:.3f}秒")

            logger.info(
                colored(
                    f"已发送改航结果到{self.flighttask_method_topic}: {status_message}",
                    "green",
                )
            )

        total_time = time.time() - start_time
        logger.debug(f"改航请求总处理耗时: {total_time:.3f}秒")

    def _handle_no_fly_zone(self, request):
        """处理禁飞区消息"""
        start_time = time.time()

        # 记录处理开始
        logger.debug(f"开始处理禁飞区消息: {request.get('no_fly_zone_name')}")

        handler_type = "no_fly_zone"
        handler = self.path_planning.handlers[handler_type]

        # 计时处理消息的时间
        handle_start = time.time()
        conflicts, _ = handler.handle_message(request)
        handle_time = time.time() - handle_start
        logger.debug(f"禁飞区处理器处理消息耗时: {handle_time:.3f}秒")

        if not conflicts:
            # logger.info(f"禁飞区 {request.get('no_fly_zone_name')} 未与任何路径冲突")
            return

        # 计时发送冲突消息的时间
        if conflicts["待执行"]:  # 确保有返回数据
            send_start = time.time()
            self._send_conflict_message(conflicts["待执行"])
            send_time = time.time() - send_start
            logger.debug(f"发送冲突消息耗时: {send_time:.3f}秒")

        if conflicts["执行中"]:
            self._handle_batch_reroute_requests(conflicts["执行中"])

        total_time = time.time() - start_time
        logger.debug(f"禁飞区消息总处理耗时: {total_time:.3f}秒")

    def _handle_batch_reroute_requests(self, uav_list: List[dict]):
        """批量处理无人机改航请求

        Args:
            uav_list: 无人机列表，每个元素必须包含：
                - flight_id: 航班唯一标识
                - posPre: 当前位置信息（格式需与_handle_reroute_request兼容）
        """
        total = len(uav_list)
        success = 0
        failures = []

        logger.info(f"开始批量处理 {total} 个无人机改航请求")

        for idx, uav in enumerate(uav_list, 1):
            try:
                # 参数验证
                if "flight_id" not in uav or "posPre" not in uav:
                    raise ValueError("缺少必要字段flight_id或posPre")

                # 构建请求格式
                request = {
                    **uav,
                    "posPreisGrid": True,
                    "data": {
                        # "posFinal": uav["posPre"],
                        # "source": uav.get("source", "batch_reroute")  # 设置默认来源
                    },
                }

                # 调用现有改航处理方法
                self._handle_reroute_request(request)
                success += 1

                # 每处理10个记录进度
                # if idx % 10 == 0:
                #     logger.debug(f"已处理 {idx}/{total} 个请求，成功率：{success/idx:.1%}")

            except Exception as e:
                failures.append(uav["flight_id"])
                logger.error(
                    f"处理无人机{uav.get('flight_id', '未知')}失败：{str(e)}",
                    exc_info=settings.settings.logging.verbose,  # 根据配置决定是否记录堆栈
                )

        # 汇总日志
        logger.info(
            f"批量改航处理完成，总计：{total}，成功：{success}，失败：{len(failures)}\n"
            f"失败航班：{failures if failures else '无'}"
        )

    def _handle_state_change(self, request):
        """处理状态变更"""
        handler_type = "state"
        handler = self.path_planning.handlers[handler_type]
        handler.handle_message(request)

        desc = request.get("desc")

        if desc in ["待执行"]:
            # 判断是否和禁飞区冲突
            obstacle_points = (
                handler.map.obstacle_manager.get_all_no_fly_zone_positions()
            )
            if obstacle_points:
                logger.info("系统中存在禁飞区，开始检查待执行航班是否冲突")
                grid_nodes = handler.planned_paths[request["id"]]["path_nodes"]
                grid_path = [(node.y, node.x, node.z) for node in grid_nodes]
                path_set = set(grid_path)
                if path_set.intersection(obstacle_points):
                    logger.info(
                        f"待执行航班 {request['id']} 与禁飞区发生冲突，需重新规划路径"
                    )
                    # 发送冲突消息
                    conflict = {"flight_id": request["id"], "source": "待执行"}
                    self._send_conflict_message([conflict])

    def _handle_risk_assessment(self, request):
        """处理风险评估消息"""
        # 获取任务ID
        task_id = request.get("id")
        if not task_id:
            logger.error("Missing task id")
            return

        # 从Redis获取数据，带重试机制
        redis_key = f"online_flighttask:{task_id}"
        task_data = None
        max_retries = 5  # 最大重试次数
        retry_interval = 0.5  # 重试间隔（秒）

        for retry in range(max_retries):
            task_data = self.redis_client.get(redis_key)
            if task_data:
                break

            if retry < max_retries - 1:  # 如果不是最后一次重试，等待后重试
                logger.warning(
                    f"Redis数据未就绪，等待{retry_interval}秒后重试 ({retry + 1}/{max_retries})"
                )
                time.sleep(retry_interval)
                # 每次重试增加等待时间
                retry_interval *= 1.5

        if not task_data:
            logger.error(f"{max_retries} 尝试后，然后无法获取任务数据: task {task_id}")
            return

        # 转换任务数据
        task_data = json.loads(task_data)
        # logger.info(f"收到消息: {task_data}")
        logger.info(colored(f"收到消息: {task_data}", "cyan"))

        if task_data["desc"] != "风险评估":
            logger.error(f"Task {task_id} is not a risk assessment task")
            return

        # 使用现有的handlers处理数据
        # task_data = task_data["data"]
        handler_type = self.path_planning._determine_handler(task_data["data"])
        handler = self.path_planning.handlers[handler_type]

        # visualizer = Visualizer3D(handler.map)
        # visualizer.plot_obstacles()

        # 处理数据
        data, _ = handler.handle_message(task_data, needsend=False)

        # if not error:
        self._send_message(old_data=data[0], new_data=data[1])

    def _start_flight_monitor_consumer_thread(self):
        """启动飞行监控消费者线程"""
        if self.flight_monitor_consumer is None:
            logger.warning("飞行监控消费者不存在，无法启动线程")
            return

        if (
            RiskAssessmentConsumer.flight_monitor_consumer_thread is not None
            and RiskAssessmentConsumer.flight_monitor_consumer_thread.is_alive()
        ):
            logger.info("飞行监控消费者线程已经运行中")
            return

        # 创建新线程
        RiskAssessmentConsumer.flight_monitor_consumer_thread = threading.Thread(
            target=self._consume_flight_monitor_messages,
            name="FlightMonitorConsumerThread",
        )
        RiskAssessmentConsumer.flight_monitor_consumer_thread.daemon = (
            True  # 设置为守护线程
        )
        RiskAssessmentConsumer.flight_monitor_consumer_thread.start()
        logger.info("飞行监控消费者线程已启动")

    def _consume_flight_monitor_messages(self):
        """处理no_fly_zone_and_flight_status消息的线程函数"""
        logger.info("开始监听飞行监控消息...")
        consecutive_errors = 0
        max_consecutive_errors = 5

        while True:
            # 检查关闭标志
            if hasattr(self, "_closing") and self._closing:
                logger.info("检测到关闭标志，飞行监控消费者线程正在退出...")
                break

            try:
                # 重新初始化飞行监控消费者以处理可能的连接断开
                if (
                    not hasattr(self, "flight_monitor_consumer")
                    or self.flight_monitor_consumer is None
                ):
                    # 检查是否正在关闭
                    if hasattr(self, "_closing") and self._closing:
                        logger.info("服务正在关闭，不再重新初始化飞行监控消费者")
                        break

                    logger.warning("飞行监控消费者不存在，尝试重新初始化")
                    self._init_kafka()
                    if (
                        not hasattr(self, "flight_monitor_consumer")
                        or self.flight_monitor_consumer is None
                    ):
                        logger.error("无法初始化飞行监控消费者，等待5秒后重试")
                        time.sleep(5)
                        continue

                # 使用轮询方式获取消息，现在可以一次获取多条消息
                start_poll_time = time.time()
                messages = self.flight_monitor_consumer.poll(
                    timeout_ms=150,
                    max_records=20,  # 与max_poll_records配置保持一致，降低内存使用
                )
                poll_time = time.time() - start_poll_time

                # 如果没有消息，短暂休眠后继续下一轮轮询
                if not messages:
                    # 重置连续错误计数
                    consecutive_errors = 0
                    # 添加短暂休眠，减少CPU使用
                    time.sleep(0.1)
                    continue

                # 记录获取到的消息数量
                total_messages = sum(len(msgs) for msgs in messages.values())
                if total_messages > 1:
                    logger.info(
                        f"飞行监控消费者一次获取到 {total_messages} 条消息，轮询耗时: {poll_time:.3f}秒"
                    )

                # 批量处理开始时间
                batch_start_time = time.time()
                processed_count = 0
                error_count = 0
                reroute_count = 0
                no_fly_zone_count = 0
                unknown_count = 0

                # 处理消息
                for _, partition_messages in messages.items():
                    for message in partition_messages:
                        try:
                            process_start_time = time.time()
                            request = message.value

                            # 改航请求
                            if (
                                "flag" in request
                                and not request["flag"]
                                and request.get("posFlag") == "1"
                            ):
                                self._handle_reroute_request(request)
                                process_time = time.time() - process_start_time
                                if total_messages <= 1:  # 只有一条消息时才记录详细日志
                                    logger.debug(
                                        f"处理改航请求耗时: {process_time:.3f}秒"
                                    )
                                # 成功处理后增加计数
                                processed_count += 1
                                reroute_count += 1
                                # 重置连续错误计数
                                consecutive_errors = 0

                            # 禁飞区
                            elif "no_fly_zone_name" in request:
                                self._handle_no_fly_zone(request)
                                process_time = time.time() - process_start_time
                                if total_messages <= 1:  # 只有一条消息时才记录详细日志
                                    logger.debug(
                                        f"处理禁飞区消息耗时: {process_time:.3f}秒"
                                    )
                                # 成功处理后增加计数
                                processed_count += 1
                                no_fly_zone_count += 1
                                # 重置连续错误计数
                                consecutive_errors = 0
                            else:
                                logger.debug(f"未知消息类型，跳过处理: {request}")
                                unknown_count += 1

                        except Exception as e:
                            logger.error(f"处理消息时出错: {str(e)}")
                            error_count += 1
                            consecutive_errors += 1
                            # 如果连续错误过多，重新初始化消费者
                            if consecutive_errors >= max_consecutive_errors:
                                logger.warning(
                                    f"连续 {consecutive_errors} 次处理错误，尝试重新初始化消费者"
                                )
                                try:
                                    if hasattr(self, "flight_monitor_consumer"):
                                        self.flight_monitor_consumer.close()
                                    self.flight_monitor_consumer = None
                                    self._init_kafka()
                                    consecutive_errors = 0
                                except Exception as reinit_error:
                                    logger.error(
                                        f"重新初始化消费者失败: {str(reinit_error)}"
                                    )
                                break
                            continue  # 继续处理下一条消息

                # 批量处理完成后提交偏移量
                try:
                    commit_start_time = time.time()
                    self.flight_monitor_consumer.commit()
                    commit_time = time.time() - commit_start_time

                    # 记录批量处理性能指标
                    batch_time = time.time() - batch_start_time
                    if total_messages > 1:
                        logger.info(
                            f"飞行监控消费者批量处理完成: 总消息 {total_messages} 条，"
                            f"成功 {processed_count} 条 (改航: {reroute_count}, 禁飞区: {no_fly_zone_count}, 未知: {unknown_count})，"
                            f"失败 {error_count} 条，处理耗时 {batch_time:.3f}秒，提交耗时 {commit_time:.3f}秒"
                        )
                    else:
                        logger.debug(colored("已提交飞行监控消费者偏移量", "yellow"))

                    # 成功提交后重置错误计数
                    consecutive_errors = 0
                except Exception as e:
                    logger.warning(f"飞行监控消费者提交偏移量失败: {str(e)}")
                    # 不因为提交失败就中断处理

            except Exception as e:
                # 检查关闭标志
                if hasattr(self, "_closing") and self._closing:
                    logger.info(f"服务正在关闭，飞行监控消费者线程退出: {str(e)}")
                    break

                logger.error(f"飞行监控消费者错误: {str(e)}")
                consecutive_errors += 1

                try:
                    # 关闭现有连接
                    if hasattr(self, "flight_monitor_consumer"):
                        self.flight_monitor_consumer.close()
                    self.flight_monitor_consumer = None
                except:
                    pass

                # 等待后重试，根据连续错误次数增加等待时间
                wait_time = min(5 * consecutive_errors, 30)  # 最多等待30秒
                logger.info(f"等待 {wait_time} 秒后重试连接...")
                time.sleep(wait_time)
                continue

    def start_consuming(self):
        """开始消费消息并处理 - 现在只是等待线程执行"""
        logger.info("风险评估服务已启动，等待消息处理...")
        try:
            # 无限循环保持主线程运行
            while True:
                time.sleep(60)  # 每分钟检查一次
                logger.debug("主线程心跳检查 - 服务正常运行中")
        except KeyboardInterrupt:
            logger.info("接收到终止信号，正在关闭服务...")
            self.close()

    def _close_producer_with_timeout(self, producer, timeout=2):
        """
        在单独的线程中关闭生产者，避免阻塞主线程

        Args:
            producer: 要关闭的生产者
            timeout: 关闭超时时间（秒）
        """
        try:
            # 记录开始时间
            start_time = time.time()

            # 尝试关闭生产者
            logger.debug(f"开始关闭生产者，超时时间: {timeout}秒")
            producer.close(timeout=timeout)

            # 记录关闭耗时
            close_time = time.time() - start_time
            logger.debug(f"生产者关闭成功，耗时: {close_time:.1f}秒")
        except Exception as e:
            logger.warning(f"生产者关闭失败: {str(e)}")

    def _close_consumer_with_timeout(self, consumer, consumer_name, timeout=5):
        """
        在单独的线程中关闭消费者，避免阻塞主线程

        Args:
            consumer: 要关闭的消费者
            consumer_name: 消费者名称（用于日志）
            timeout: 关闭超时时间（秒）
        """
        try:
            # 记录开始时间
            start_time = time.time()

            # 尝试关闭消费者
            logger.debug(f"开始关闭 {consumer_name}，超时时间: {timeout}秒")
            consumer.close(autocommit=False)

            # 记录关闭耗时
            close_time = time.time() - start_time
            logger.debug(f"{consumer_name} 关闭成功，耗时: {close_time:.1f}秒")
        except Exception as e:
            logger.warning(f"{consumer_name} 关闭失败: {str(e)}")

    def _force_terminate_threads(self):
        """强制终止所有可能仍在运行的线程（最后的手段）"""
        try:
            # 获取所有活跃线程
            active_threads = threading.enumerate()
            daemon_threads = [
                t
                for t in active_threads
                if t.daemon and t != threading.current_thread()
            ]

            if daemon_threads:
                logger.warning(
                    f"发现 {len(daemon_threads)} 个仍在运行的守护线程，尝试强制终止"
                )

                # 尝试终止Kafka相关线程
                kafka_threads = [t for t in daemon_threads if "kafka" in t.name.lower()]
                if kafka_threads:
                    logger.warning(
                        f"发现 {len(kafka_threads)} 个Kafka相关线程: {[t.name for t in kafka_threads]}"
                    )

                # 尝试终止定时器线程
                timer_threads = [t for t in daemon_threads if "timer" in t.name.lower()]
                if timer_threads:
                    logger.warning(
                        f"发现 {len(timer_threads)} 个定时器线程: {[t.name for t in timer_threads]}"
                    )

                # 在Python中无法直接终止线程，但可以设置标志并等待
                # 这里我们只记录信息，依赖守护线程的特性在主线程结束时自动终止
                logger.info("所有守护线程将在主线程结束时自动终止")
        except Exception as e:
            logger.error(f"强制终止线程时出错: {str(e)}")

    def _process_messages_in_parallel(self, messages):
        """
        并行处理消息

        Args:
            messages: 消息列表，每个消息是一个字典

        Returns:
            处理结果列表
        """
        # 如果没有消息，直接返回
        if not messages:
            return []

        # 记录开始时间
        start_time = time.time()

        # 对消息进行分组
        logger.info(f"开始对 {len(messages)} 条消息进行分组...")
        message_groups = self.parallel_processor.group_messages(messages)
        group_time = time.time() - start_time

        # 记录分组结果
        group_sizes = [len(group) for group in message_groups]
        logger.info(
            f"消息分组完成，共 {len(message_groups)} 个组，各组大小: {group_sizes}，耗时: {group_time:.3f}秒"
        )

        # 定义单个消息处理函数
        def process_single_message(message):
            try:
                # 根据消息类型进行处理
                desc = message.get("desc")

                if desc == "风险评估":
                    return self._handle_risk_assessment(message)
                elif desc in [
                    "审批驳回",
                    "审批通过",
                    "待执行",
                    "执行中",
                    "结束-任务取消",
                    "结束-执行异常",
                ]:
                    return self._handle_state_change(message)
                else:
                    logger.debug(f"未处理的状态机消息类型: {desc}")
                    return None
            except Exception as e:
                logger.error(f"处理消息时出错: {str(e)}")
                return None

        # 并行处理消息组
        logger.info("开始并行处理消息组...")
        results = self.parallel_processor.process_message_groups(
            message_groups, process_single_message
        )

        # 计算总处理时间
        total_time = time.time() - start_time

        # 统计处理结果
        success_count = sum(1 for r in results if r.get("success", False))
        logger.info(
            f"并行处理完成，总消息数: {len(messages)}，成功处理: {success_count}，总耗时: {total_time:.3f}秒"
        )

        return results

    def close(self):
        """强制关闭所有连接和定时器（1秒内完成）"""
        # 记录开始时间
        close_start_time = time.time()

        try:
            # 设置所有关闭标志
            self._closing = True
            from .message_handlers.base import MessageHandler
            from ..utils.db_connection_manager import DBConnectionManager

            MessageHandler.is_shutting_down = True
            DBConnectionManager.is_shutting_down = True

            # 设置全局关闭标志
            import sys

            if hasattr(sys, "_is_shutting_down"):
                sys._is_shutting_down = True
            threading._shutdown = True

            logger.info("开始强制关闭服务...（1秒内完成）")

            # 首先尝试终止所有线程，避免在关闭资源时线程仍在访问
            try:
                # 先终止所有线程，避免在关闭资源时线程仍在访问
                self._force_terminate_threads()
            except Exception as thread_error:
                logger.error(f"终止线程时出错: {str(thread_error)}")

            # 立即强制关闭所有资源 - 不等待优雅关闭
            # 1. 立即关闭所有Kafka消费者
            if RiskAssessmentConsumer.state_machine_consumer:
                try:
                    RiskAssessmentConsumer.state_machine_consumer = None
                    logger.info("状态机消费者已强制关闭")
                except Exception as e:
                    logger.warning(f"关闭状态机消费者时出错: {str(e)}")

            if (
                hasattr(self, "flight_monitor_consumer")
                and self.flight_monitor_consumer
            ):
                try:
                    self.flight_monitor_consumer = None
                    logger.info("飞行监控消费者已强制关闭")
                except Exception as e:
                    logger.warning(f"关闭飞行监控消费者时出错: {str(e)}")

            # 2. 立即关闭Kafka生产者
            if hasattr(self, "producer") and self.producer:
                try:
                    self.producer = None
                    logger.info("Kafka生产者已强制关闭")
                except Exception as e:
                    logger.warning(f"关闭Kafka生产者时出错: {str(e)}")

            # 确保 MessageHandler 中的生产者也被关闭
            try:
                MessageHandler.close_kafka_producer()
            except Exception as e:
                logger.warning(f"关闭MessageHandler中的Kafka生产者时出错: {str(e)}")

            # 3. 立即关闭定时器管理器
            if (
                hasattr(RiskAssessmentConsumer, "timer_manager")
                and RiskAssessmentConsumer.timer_manager
            ):
                try:
                    RiskAssessmentConsumer.timer_manager = None
                    logger.info("定时器管理器已强制关闭")
                except Exception as e:
                    logger.warning(f"关闭定时器管理器时出错: {str(e)}")

            # 4. 立即关闭统一定时器管理器
            if (
                hasattr(RiskAssessmentConsumer, "unified_timer_manager")
                and RiskAssessmentConsumer.unified_timer_manager
            ):
                try:
                    # 先调用shutdown方法
                    if hasattr(
                        RiskAssessmentConsumer.unified_timer_manager, "shutdown"
                    ):
                        RiskAssessmentConsumer.unified_timer_manager.shutdown()
                    RiskAssessmentConsumer.unified_timer_manager = None
                    logger.info("统一定时器管理器已强制关闭")
                except Exception as e:
                    logger.warning(f"关闭统一定时器管理器时出错: {str(e)}")

            # 5. 立即关闭Redis连接
            if RiskAssessmentConsumer.redis_client:
                try:
                    RiskAssessmentConsumer.redis_client = None
                    logger.info("Redis连接已强制关闭")
                except Exception as e:
                    logger.warning(f"关闭Redis连接时出错: {str(e)}")

            # 6. 立即关闭并行处理器
            if hasattr(self, "parallel_processor"):
                try:
                    self.parallel_processor = None
                    logger.info("并行处理器已强制关闭")
                except Exception as e:
                    logger.warning(f"关闭并行处理器时出错: {str(e)}")

            # 7. 关闭数据库连接
            try:
                MessageHandler.close_connections()
                logger.info("数据库连接已强制关闭")
            except Exception as e:
                logger.warning(f"关闭数据库连接时出错: {str(e)}")

            # 8. 再次强制终止所有线程，确保所有线程都被终止
            try:
                self._force_terminate_threads()
            except Exception as thread_error:
                logger.error(f"最终终止线程时出错: {str(thread_error)}")

            # 记录关闭耗时
            close_elapsed = time.time() - close_start_time
            logger.info(f"所有资源已强制关闭，总耗时: {close_elapsed:.3f}秒")

        except Exception as e:
            logger.error(f"强制关闭过程中出错: {str(e)}")
            # 即使出错，也尝试强制终止线程
            try:
                self._force_terminate_threads()
            except Exception as thread_error:
                logger.error(f"强制终止线程时出错: {str(thread_error)}")

        # 确保在所有情况下都尝试终止线程
        finally:
            try:
                self._force_terminate_threads()
            except Exception as thread_error:
                logger.error(f"最终强制终止线程时出错: {str(thread_error)}")

    def _force_terminate_threads(self):
        """强制终止所有线程"""
        try:
            # 获取所有活跃线程
            active_threads = threading.enumerate()

            # 排除主线程
            active_threads = [
                t for t in active_threads if t != threading.current_thread()
            ]

            if active_threads:
                logger.info(f"尝试强制终止 {len(active_threads)} 个活跃线程")

                # 分类线程
                kafka_threads = []
                timer_threads = []
                other_threads = []

                for thread in active_threads:
                    try:
                        thread_name = (
                            thread.name.lower()
                            if hasattr(thread, "name")
                            else "unknown"
                        )
                        if "kafka" in thread_name:
                            kafka_threads.append(thread)
                        elif "timer" in thread_name:
                            timer_threads.append(thread)
                        else:
                            other_threads.append(thread)
                    except Exception as e:
                        logger.warning(f"分类线程时出错: {str(e)}")
                        other_threads.append(thread)

                # 记录各类线程数量
                if kafka_threads:
                    logger.info(f"发现 {len(kafka_threads)} 个Kafka相关线程")
                if timer_threads:
                    logger.info(f"发现 {len(timer_threads)} 个定时器线程")
                if other_threads:
                    logger.info(f"发现 {len(other_threads)} 个其他线程")

                # 设置所有线程为守护线程
                for thread in active_threads:
                    try:
                        # 检查线程是否有效
                        if not hasattr(thread, "_ident") or thread._ident is None:
                            logger.warning(
                                f"线程 {thread.name if hasattr(thread, 'name') else 'unknown'} 的_ident为None，跳过处理"
                            )
                            continue

                        thread_name = (
                            thread.name if hasattr(thread, "name") else "unknown"
                        )
                        if not thread.daemon and thread.is_alive():
                            thread.daemon = True
                            logger.debug(f"将线程 {thread_name} 设置为守护线程")
                    except Exception as e:
                        logger.warning(f"设置线程为守护线程时出错: {str(e)}")

                # 记录仍在运行的线程
                running_threads = []
                for thread in active_threads:
                    try:
                        if hasattr(thread, "is_alive") and thread.is_alive():
                            thread_name = (
                                thread.name if hasattr(thread, "name") else "unknown"
                            )
                            running_threads.append(thread_name)
                    except Exception as e:
                        logger.warning(f"检查线程状态时出错: {str(e)}")

                if running_threads:
                    logger.debug(f"以下线程仍在运行: {', '.join(running_threads)}")
                    logger.info("所有守护线程将在主线程结束时自动终止")
            else:
                logger.info("没有需要强制终止的线程")

        except Exception as e:
            logger.error(f"强制终止线程时出错: {str(e)}")
