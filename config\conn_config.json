{"kafka": {"kafka_producer": {"topic": "dji", "topic_control": "dji_method_info_reply", "bootstrap_servers": ["*************:9093"], "key_serializer": "json", "value_serializer": "json"}, "kafka_consumer": {"topic": "dji_method_info", "bootstrap_servers": ["*************:9093"], "key_deserializer": "string", "value_deserializer": "json", "group_id": "ctrl01", "client_id": "ctrl", "max_poll_records": 20, "consumer_timeout_ms": 1000, "auto_offset_reset": "latest"}}, "mqtt": {"client_id": "djiport_30a1a30a", "broker_host": "************", "broker_port": 1883, "keep_alive": 60, "use_ssl": false, "username": "emqx_u_4", "password": "emqx4@2345", "topics": ["thing/product/{device_sn}/osd", "thing/product/{device_sn}/state", "thing/product/{gateway_sn}/osd", "thing/product/{gateway_sn}/state", "thing/product/{gateway_sn}/services", "thing/product/{gateway_sn}/services_reply", "sys/product/{gateway_sn}/status", "sys/product/{gateway_sn}/status_reply", "thing/product/{gateway_sn}/requests", "thing/product/{gateway_sn}/requests_reply", "thing/product/{gateway_sn}/events", "thing/product/{gateway_sn}/events_reply", "thing/product/{gateway_sn}/property/set", "thing/product/{gateway_sn}/property/set_reply", "thing/product/{gateway_sn}/drc/up", "thing/product/{gateway_sn}/drc/down"]}, "mysql": {"hosts": "*************", "user": "root", "password": "root", "port": 3306, "db": "jeecg-boot-dev"}, "redis": {"connect": {"hosts": "*************", "port": 6379, "password": "", "db": 1}}, "oss": {"bucket": "media", "credentials": {"expire": 3600, "access_key_id": "", "access_key_secret": "", "security_token": ""}, "object_key_prefix": "video", "endpoint": "http://***************:9001", "provider": "minio", "region": "cn-beijing-2"}, "minio": {"expire": 3600, "access_key": "admin", "secret_key": "zqf123456", "role_arn": "arn:aws:s3:::media/*", "role_session_name": "sts_test1"}, "debuginfo": 0}