from typing import Dict, List, Optional, <PERSON><PERSON>
from datetime import datetime
from ...utils.logging import get_logger
from ...core.validation.route_validator import RouteValidator
from .base import MessageHandler
from ...core.node_3d import GridNode3D

logger = get_logger(__name__)


class RouteMessageHandler(MessageHandler):
    """处理固定航线消息"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # 初始化验证器
        self.validator = RouteValidator(self.map, self.occupancy_map)

    def handle_message(
        self, message: Dict, needsend=True
    ) -> Tuple[Optional[List], Optional[str]]:
        """
        处理固定航线验证消息

        Args:
            message: 消息字典，包含航线信息

        Returns:
            Tuple[Optional[List], Optional[str]]: (路径点列表, 错误信息)
        """
        try:
            message_data = message.get("data", {})
            # 处理新的固定航线验证请求
            begin_time = self.parse_time(message_data["beginTime"])
            time_direction = message_data.get("time_direction", 0)  # 默认双向搜索
            max_offset = message_data.get("max_offset", 3600)  # 默认1小时偏移

            grid_coords, grid_nodes, error, valid_time, time_modified = (
                self.validator.validate_route(
                    message_data["uavrootId"],
                    message_data["flightapplyid"],
                    begin_time,
                    time_direction,
                    max_offset,
                )
            )

            if grid_coords and grid_nodes:
                # 如果验证通过，添加到占用图
                self.occupancy_map.add_path(grid_nodes, message_data["flightapplyid"])

                # # 生成地理坐标路径
                # path_geo = []
                # for i, (y, x, z) in enumerate(grid_coords):
                #     coords = self.grid_converter.relative_to_geo(y, x, z)
                #     path_geo.append(
                #         {
                #             "lat": coords["lat"],
                #             "lng": coords["lon"],
                #             "height": coords["alt"],
                #             "time": valid_time + i,
                #             "index": i,
                #         }
                #     )

                # 存储路径
                self.store_path(
                    message_data["flightapplyid"],
                    grid_nodes,  # 完整node路径列表
                    grid_nodes,  # 完整node路径列表
                    message,
                )

                adjustment_count = message_data.get("adjustment_count", 0)
                # 计算结束时间
                end_time_str = datetime.fromtimestamp(grid_nodes[-1].t).strftime(
                    "%Y-%m-%d %H:%M:%S"
                )
                # 根据时间修改情况更新响应信息
                if time_modified:
                    # 构造各种时间字符串
                    valid_time_str = datetime.fromtimestamp(valid_time).strftime(
                        "%Y-%m-%d %H:%M:%S"
                    )
                    response_data = {
                        # "planned_path_points": path_geo,
                        "takeoff_time": valid_time_str,
                        "landing_time": end_time_str,
                        "adjustment_count": adjustment_count + 1,
                        "risk_state": True,
                        "risk_reason": "与计划航线冲突，建议调整起飞时间",
                    }
                else:
                    response_data = {
                        # "planned_path_points": path_geo,
                        "landing_time": end_time_str,
                        "adjustment_count": adjustment_count,
                        "risk_state": True,
                        "risk_reason": "固定航线验证通过",
                    }

                # if needsend: # 直接发给无人机
                # self._send_response(
                #     request=message_data,
                #     new_data={**response_data, "changeroute": False},
                #     response_topic=self.uav_topic,
                # )
                response_data["planned_path_points"] = []  # 固定路径评估不需要返回路径
                return (message_data, response_data), None

            response_data = {
                "risk_state": False,
                "risk_reason": error or "固定航线验证失败",
            }
            if needsend:
                # 发送失败响应
                self._send_response(
                    request=message_data,
                    new_data=response_data,
                    response_topic=self.response_topic,
                )
            return (message_data, response_data), error
            # return (
            #     message_data,
            #     {
            #         "risk_state": True,
            #         "risk_reason": "固定航线验证通过",
            #         "adjustment_count": 0,
            #         "task_source": "1",
            #     },
            # ), None

        except Exception as e:
            error_msg = f"处理固定航线消息时出错: {str(e)}"
            logger.error(error_msg)
            new_data = {"risk_state": False, "risk_reason": error_msg}
            return (message_data, new_data), error_msg
            # return (
            #     message_data,
            #     {
            #         "risk_state": True,
            #         "risk_reason": "固定航线验证通过",
            #         "adjustment_count": 0,
            #         "task_source": "1",
            #     },
            # ), None
