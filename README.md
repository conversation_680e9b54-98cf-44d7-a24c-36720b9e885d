# README.md

## 项目名称：idspace_app
低空空域管控系统统一后端框架，支持多个子模块独立部署，统一调用公共库，适用于 Redis/MQTT/MySQL/Kafka/OSS 等集成场景。

## 项目结构概览
idspace_app/
├── common_lib/                  # 公共库
│   ├── __init__.py
│   ├── config_loader.py
│   ├── logger.py
│   ├── redis_mgr.py
│   ├── mysql_mgr.py
│   ├── kafka_mgr.py
│   ├── mqtt_mgr.py
│   ├── oss_mgr.py
│   └── tools.py
│
├── config/                       # 全局连接配置（如 Redis/MQTT 统一连接信息）
│   └── conn_config.json
│
├── dji_port/                     # 示例模块：含私有config 与业务逻辑
│   ├── __init__.py
│   ├── __main__.py 
│   ├── Dockerfile                 # 单模块docker构建文件
│   ├── all_data.py
│   ├── control_strategy.py
│   ├── rev_mqtt.py
│   ├── send_mqtt.py
│   └── config/                    # 模块私有数据配置
│       ├── app_config.json
│       ├── events_reply.json
│       ├── methods_name.json
│       ├── requests_reply.json
│       └── to/
│           └── emqxsl-ca.crt
│
├── radarInter/                    # 同级目录补充其他模块
│   ├── __init__.py
│   ├── __main__.py
│   └── Dockerfile
│
├── requirements.txt            # 项目统一依赖文件
├── environment.yml             # conda环境定义文件（启动时推荐使用）
├── docker-compose.yml          # 一键多模块启动编排
├── .dockerignore
├── README.md

## 代码启动方式：使用 Miniconda 本地开发调试
miniconda安装详见《python开发环境升级操作手册》

```bash
# 创建并激活conda环境
cd idspace_app
conda env create -f environment.yml   #基于 environment.yml 创建 Conda 环境
conda activate 3.9.13

# 启动某个模块（如 dji_port）
python -m dji_port
```

## 打包部署方式
### ✅ 方法一：使用 Docker 单模块构建运行
```Dockerfile
RUN pip install -r /app/requirements.txt  
```

**构建并运行单模块镜像：** 
```bash
cd idspace_app

docker build -t idspace/dji_port:0.1.0 -f dji_port/Dockerfile .

docker run --rm -it \
  -v $(pwd)/config:/app/config \
  -e IDSPACE_CONFIG=/app/config/conn_config.json \
  idspace/dji_port:0.1.0
```

### ✅ 方法二：使用 Docker Compose 一键启动整个项目（推荐部署方式）
```bash
cd idspace_app
docker-compose up --build
```
此方法将同时启动：
- `dji_port` 模块容器
- `redis` 容器
- 后续可加入更多模块（如 `radarInter`...）



## 如何添加新模块（如 flighttask）
1. 复制 `dji_port/` 目录结构为 `flighttask/`
2. 编辑内容：
   - 修改 `__main__.py` 为新的模块入口逻辑
   - 可保留 `config/` 子目录用于私有配置
   - 编写业务代码
3. 使用统一依赖（无需新增 requirements.txt），新依赖包增加在项目外层的requirements.txt
4. 添加 `flighttask/Dockerfile`
5. 将其加入 `docker-compose.yml` 中：
```yaml
  flighttask:
    build:
      context: .
      dockerfile: flighttask/Dockerfile
    depends_on:
      - redis
    networks:
      - idspace_net
```

## 公共库使用方式（common_lib）

各模块统一调用公共功能模块，例如：

```python
from common_lib.mysqlclient import MySqlClient
from common_lib.ossclient import OssClient
```

| 模块名         | 功能说明              |
|----------------|----------------------|
| redis_mgr.py   | Redis 通用封装        |
| mysqlclient.py | MySQL 通用封装        |
| mqtt_mgr.py    | MQTT 消息管理         |
| kafka_mgr.py   | Kafka 消息管理        |
| ossclient.py   | OSS 文件上传封装       |
| tools.py       | 通用工具类（如加解密、UUID） |