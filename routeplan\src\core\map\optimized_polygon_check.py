import numpy as np

# Try to import Numba for JIT compilation (optional)
try:
    from numba import jit, prange

    HAS_NUMBA = True
except ImportError:
    HAS_NUMBA = False

    # Create dummy decorators
    def jit(*args, **kwargs):
        def decorator(func):
            return func

        return decorator

    def prange(*args):
        return range(*args)


def points_in_polygon_vectorized(points, polygon):
    """
    Fully vectorized implementation of the ray casting algorithm to check if points are inside a polygon.

    Args:
        points: nx2 array of points to check, each row is [y, x]
        polygon: mx2 array of polygon vertices, each row is [y, x]

    Returns:
        Boolean array of length n indicating whether each point is inside the polygon
    """
    # For very large datasets, use the grid-based approach
    if len(points) > 1_000_000:
        return points_in_polygon_grid_based(points, polygon)

    # Get next vertex indices (wrap around to first vertex)
    n_vertices = len(polygon)
    next_vertices = np.roll(np.arange(n_vertices), -1)

    # Prepare arrays for broadcasting
    # Reshape points to (n, 1, 2) for broadcasting with polygon vertices
    points_expanded = points[:, np.newaxis, :]

    # Get polygon edges as pairs of vertices
    polygon_edges = np.stack(
        (polygon, polygon[next_vertices]), axis=1
    )  # shape: (n_vertices, 2, 2)

    # Check if point is on the same side of the ray as vertex
    # For each point and each edge, check if the edge crosses a ray from the point to the right
    y_check = (polygon_edges[:, 0, 1] > points_expanded[:, :, 1]) != (
        polygon_edges[:, 1, 1] > points_expanded[:, :, 1]
    )

    # For edges that cross the ray, calculate the x-coordinate of intersection
    # and check if it's to the right of the point
    where_y_check = np.where(y_check)

    # Initialize result array (all False)
    result = np.zeros(len(points), dtype=bool)

    # Only process points and edges where y_check is True
    if len(where_y_check[0]) > 0:
        point_indices = where_y_check[0]
        edge_indices = where_y_check[1]

        # Get relevant points and edges
        relevant_points = points[point_indices]
        relevant_edges_start = polygon[edge_indices]
        relevant_edges_end = polygon[next_vertices[edge_indices]]

        # Calculate x-coordinate of intersection
        # Add a small epsilon to avoid division by zero
        denominator = relevant_edges_end[:, 1] - relevant_edges_start[:, 1]
        # Avoid division by zero
        safe_denominator = np.where(denominator == 0, np.finfo(float).eps, denominator)

        x_intersect = (relevant_edges_end[:, 0] - relevant_edges_start[:, 0]) * (
            relevant_points[:, 1] - relevant_edges_start[:, 1]
        ) / safe_denominator + relevant_edges_start[:, 0]

        # Check if intersection is to the right of the point
        x_check = relevant_points[:, 0] < x_intersect

        # Count crossings for each point
        # We use bincount with point_indices as weights to count how many edges cross the ray for each point
        crossings = np.bincount(point_indices[x_check], minlength=len(points))

        # A point is inside if the ray crosses an odd number of edges
        result = crossings % 2 == 1

    return result


# Define Numba-accelerated function if available
@jit(nopython=True, parallel=True)
def _point_in_polygon_numba_impl(points, polygon):
    """Numba-accelerated point-in-polygon check for large datasets"""
    n_points = len(points)
    n_vertices = len(polygon)
    result = np.zeros(n_points, dtype=np.bool_)

    # For each point
    for i in prange(n_points):
        inside = False
        x, y = points[i]

        # Ray casting algorithm
        for j in range(n_vertices):
            j_next = (j + 1) % n_vertices
            yi, xi = polygon[j]
            yj, xj = polygon[j_next]

            # Avoid division by zero
            if abs(yj - yi) < 1e-10:
                continue

            intersect = ((yi > y) != (yj > y)) and (
                x < (xj - xi) * (y - yi) / (yj - yi) + xi
            )
            if intersect:
                inside = not inside

        result[i] = inside

    return result


# Choose the appropriate implementation based on Numba availability
def _point_in_polygon_numba(points, polygon):
    if HAS_NUMBA:
        return _point_in_polygon_numba_impl(points, polygon)
    else:
        return points_in_polygon_vectorized_simple(points, polygon)


def points_in_polygon_vectorized_simple(points, polygon):
    """
    Alternative vectorized implementation that's simpler but may be less efficient for very large datasets.

    Args:
        points: nx2 array of points to check, each row is [y, x]
        polygon: mx2 array of polygon vertices, each row is [y, x]

    Returns:
        Boolean array of length n indicating whether each point is inside the polygon
    """
    n_points = len(points)
    n_vertices = len(polygon)

    # Get next vertex indices (wrap around to first vertex)
    j = np.roll(np.arange(n_vertices), -1)

    # Initialize result array
    result = np.zeros(n_points, dtype=bool)

    # For each edge of the polygon
    for k in range(n_vertices):
        # Check if the edge crosses a ray from each point to the right
        # Add a small epsilon to avoid division by zero
        denominator = polygon[j[k], 1] - polygon[k, 1]
        # Skip edges that are horizontal (would cause division by zero)
        if denominator == 0:
            continue

        mask = ((polygon[k, 1] > points[:, 1]) != (polygon[j[k], 1] > points[:, 1])) & (
            points[:, 0]
            < (polygon[j[k], 0] - polygon[k, 0])
            * (points[:, 1] - polygon[k, 1])
            / denominator
            + polygon[k, 0]
        )

        # XOR the result with the mask
        result = np.logical_xor(result, mask)

    return result


def points_in_polygon_grid_based(points, polygon, grid_size=100):
    """
    Grid-based optimization for point-in-polygon checks with very large datasets.

    This approach divides the space into a grid and classifies each cell as:
    - Completely inside the polygon
    - Completely outside the polygon
    - Intersecting the polygon boundary

    Only points in the intersecting cells need detailed point-in-polygon checks.

    Args:
        points: nx2 array of points to check, each row is [y, x]
        polygon: mx2 array of polygon vertices, each row is [y, x]
        grid_size: Number of grid cells in each dimension

    Returns:
        Boolean array of length n indicating whether each point is inside the polygon
    """
    # Get bounding box of polygon
    y_min, y_max = np.min(polygon[:, 0]), np.max(polygon[:, 0])
    x_min, x_max = np.min(polygon[:, 1]), np.max(polygon[:, 1])

    # Add a small margin to ensure we include all points
    margin = 0.001 * max(y_max - y_min, x_max - x_min)
    y_min -= margin
    y_max += margin
    x_min -= margin
    x_max += margin

    # Create grid
    y_edges = np.linspace(y_min, y_max, grid_size + 1)
    x_edges = np.linspace(x_min, x_max, grid_size + 1)

    # Initialize result array
    result = np.zeros(len(points), dtype=bool)

    # For each grid cell
    for i in range(grid_size):
        for j in range(grid_size):
            # Get cell boundaries
            cell_y_min, cell_y_max = y_edges[i], y_edges[i + 1]
            cell_x_min, cell_x_max = x_edges[j], x_edges[j + 1]

            # Find points in this cell
            cell_mask = (
                (points[:, 0] >= cell_y_min)
                & (points[:, 0] < cell_y_max)
                & (points[:, 1] >= cell_x_min)
                & (points[:, 1] < cell_x_max)
            )
            cell_points = points[cell_mask]

            if len(cell_points) == 0:
                continue

            # Check if cell corners are all inside or all outside the polygon
            corners = np.array(
                [
                    [cell_y_min, cell_x_min],
                    [cell_y_min, cell_x_max],
                    [cell_y_max, cell_x_min],
                    [cell_y_max, cell_x_max],
                ]
            )

            # Use the simple vectorized method for the corners
            corners_inside = points_in_polygon_vectorized_simple(corners, polygon)

            # If all corners are inside, all points in the cell are inside
            if np.all(corners_inside):
                result[cell_mask] = True
                continue

            # If all corners are outside, we need to check if the cell intersects the polygon
            # by checking if any polygon edge intersects the cell boundary
            if not np.any(corners_inside):
                # Check if any polygon edge intersects the cell
                cell_intersects_polygon = False

                # Create cell edges
                cell_edges = [
                    # Bottom edge
                    [[cell_y_min, cell_x_min], [cell_y_min, cell_x_max]],
                    # Right edge
                    [[cell_y_min, cell_x_max], [cell_y_max, cell_x_max]],
                    # Top edge
                    [[cell_y_max, cell_x_max], [cell_y_max, cell_x_min]],
                    # Left edge
                    [[cell_y_max, cell_x_min], [cell_y_min, cell_x_min]],
                ]

                # Check if any polygon edge intersects any cell edge
                n_vertices = len(polygon)
                for v in range(n_vertices):
                    v_next = (v + 1) % n_vertices
                    poly_edge = [polygon[v], polygon[v_next]]

                    for cell_edge in cell_edges:
                        if line_segments_intersect(
                            poly_edge[0], poly_edge[1], cell_edge[0], cell_edge[1]
                        ):
                            cell_intersects_polygon = True
                            break

                    if cell_intersects_polygon:
                        break

                # If the cell doesn't intersect the polygon and all corners are outside,
                # all points in the cell are outside
                if not cell_intersects_polygon:
                    continue

            # For cells that intersect the polygon boundary, check each point individually
            if len(cell_points) > 100000:
                # Use Numba for very large cells
                cell_results = _point_in_polygon_numba(cell_points, polygon)
            else:
                # Use vectorized method for smaller cells
                cell_results = points_in_polygon_vectorized_simple(cell_points, polygon)

            result[cell_mask] = cell_results

    return result


def line_segments_intersect(p1, p2, p3, p4):
    """
    Check if line segment p1-p2 intersects with line segment p3-p4.

    Args:
        p1, p2: Endpoints of first line segment, each is [y, x]
        p3, p4: Endpoints of second line segment, each is [y, x]

    Returns:
        bool: True if the line segments intersect
    """
    # Convert to numpy arrays for easier calculation
    p1 = np.array(p1)
    p2 = np.array(p2)
    p3 = np.array(p3)
    p4 = np.array(p4)

    # Calculate direction vectors
    d1 = p2 - p1
    d2 = p4 - p3
    d3 = p3 - p1

    # Calculate the cross products
    cross_d1_d2 = np.cross(d1, d2)

    # If lines are parallel, they don't intersect (unless collinear, which we ignore)
    if abs(cross_d1_d2) < 1e-10:
        return False

    # Calculate parameters for the intersection point
    t1 = np.cross(d3, d2) / cross_d1_d2
    t2 = np.cross(d3, d1) / cross_d1_d2

    # Check if intersection point is within both line segments
    return (0 <= t1 <= 1) and (0 <= t2 <= 1)
