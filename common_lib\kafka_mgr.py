# -*- coding: utf-8 -*-
""" 卡夫卡连接使用函数库
python 版本：python3
Name：kafka_mgr.py
Description:卡夫卡连接使用函数库
# Author:胡旭
# Version:1.0
# Date:2022/11/07
"""
import copy
import json
import traceback

from kafka import KafkaConsumer, KafkaProducer, TopicPartition
from kafka.errors import KafkaError
from loguru import logger
from common_lib import tools


class KafkaProducerMgr():
    """ 卡夫卡生产者 """

    DEF_CONFIG = {
        'bootstrap_servers': ['localhost'],
        'key_serializer': None,
        'value_serializer': None,
        'topic': None
    }

    DEF_TRANS_DICT = {
        'json': lambda k: json.dumps(k).encode(),
        'string': str.encode
    }

    def __init__(self, kafka_config):
        self.kafka_config = copy.deepcopy(self.DEF_CONFIG)
        for key in self.kafka_config:
            if key in kafka_config:
                if isinstance(kafka_config[key], str):
                    if kafka_config[key] in self.DEF_TRANS_DICT:
                        self.kafka_config[key] = self.DEF_TRANS_DICT[kafka_config[key]]
                    else:
                        self.kafka_config[key] = kafka_config[key]
                else:
                    self.kafka_config[key] = kafka_config[key]

        self.init_producer()

    def init_producer(self):
        """ 初始化卡夫卡生产者
        """
        try:
            self.producer = KafkaProducer(bootstrap_servers=self.kafka_config['bootstrap_servers'],
                                          key_serializer=self.kafka_config['key_serializer'],
                                          value_serializer=self.kafka_config['value_serializer']
                                          )
            self.conn = True
        except KafkaError:
            traceback.format_exc()
            self.conn = False

    def send_to_kafka(self, send_mesage_value, topic=None, send_mesage_key=None)->bool:
        """ 向卡夫卡写信息

        Args:
            send_mesage_value (_type_): _description_
            topic (_type_, optional): _description_. Defaults to None.
            send_mesage_key (_type_, optional): _description_. Defaults to None.

        Returns:
            bool: 写信息是否成功
        """
        try:
            res = True
            if topic is None:
                topic = self.kafka_config['topic']
            if not self.conn:
                self.init_producer()
            future = self.producer.send(
                topic,
                key=send_mesage_key,
                value=send_mesage_value
            )  # 默认分区
            future.get(timeout=10)  # 监控是否发送成功
        except KafkaError:  # 发送失败抛出kafka_errors
            traceback.format_exc()
            res = False
        return res


@logger.catch
def json_ascii(data:bytes):
    """ 数据按ascii解码并生成json数据

    Args:
        data (bytes): 输入数据

    Returns:
        dict,list: json数据
    """
    try:
        message = data.decode('ascii', errors='replace')
        return json.loads(message)
    except ValueError as e:
        logger.error(f"\n卡夫卡消息<{message}>读取失败！错误信息：{e}")
        return None
    except UnicodeDecodeError as e:
        logger.error(f"\n卡夫卡消息解码失败！错误信息：{e}")
        return None


@logger.catch
def json_gb2312(data:bytes):
    """ 数据按gb2312解码并生成json数据

    Args:
        data (bytes): 输入数据

    Returns:
        dict,list: json数据
    """
    try:
        message = data.decode('gb2312', errors='replace')
        return json.loads(message)
    except ValueError as e:
        logger.error(f"\n卡夫卡消息<{message}>读取失败！错误信息：{e}")
        return None
    except UnicodeDecodeError as e:
        logger.error(f"\n卡夫卡消息解码失败！错误信息：{e}")
        return None

@logger.catch
def json_utf8(data:bytes):
    """ 数据按utf8解码并生成json数据

    Args:
        data (bytes): 输入数据

    Returns:
        dict,list: json数据
    """
    try:
        message = data.decode('utf-8', errors='replace')
        return json.loads(message)
    except ValueError as e:
        logger.error(f"\n卡夫卡消息<{message}>读取失败！错误信息：{e}")
        return None
    except UnicodeDecodeError as e:
        logger.error(f"\n卡夫卡消息解码失败！错误信息：{e}")
        return None


class KafkaConsumerMgr():
    """ 卡夫卡消费者 """

    DEF_CONFIG = {
        'bootstrap_servers': ['localhost'],
        'key_deserializer': None,
        'value_deserializer': None,
        'topic': None,
        "group_id": None,
        "client_id": None,
        "max_poll_records": 20,
        "consumer_timeout_ms": 1000,
        "auto_offset_reset": 'earliest'
    }

    DEF_TRANS_DICT = {
        'json': json_ascii,
        'json_gb2312': json_gb2312,
        'json_utf-8': json_utf8,
        'string': None
    }

    def __init__(self, kafka_config):
        self.kafka_config = copy.copy(self.DEF_CONFIG)
        for key in self.kafka_config:
            if key in kafka_config:
                if isinstance(kafka_config[key], str):
                    if kafka_config[key] in self.DEF_TRANS_DICT:
                        self.kafka_config[key] = self.DEF_TRANS_DICT[kafka_config[key]]
                    else:
                        self.kafka_config[key] = kafka_config[key]
                else:
                    self.kafka_config[key] = kafka_config[key]

        self.init_consumer()

    def __del__(self):
        self.consumer.close()

    # To consume latest messages and auto-commit offsets
    def init_consumer(self):
        """ 初始化卡夫卡消费者
        """
        try:
            self.consumer = KafkaConsumer(
                self.kafka_config['topic'],
                bootstrap_servers=self.kafka_config['bootstrap_servers'],
                key_deserializer=self.kafka_config['key_deserializer'],
                value_deserializer=self.kafka_config['value_deserializer'],
                auto_offset_reset=self.kafka_config['auto_offset_reset'],
                group_id=self.kafka_config['group_id'],
                client_id=self.kafka_config['client_id'],
                max_poll_records=self.kafka_config['max_poll_records'],
                # max_poll_interval_ms = 10,
                # fetch_max_wait_ms=100,
                consumer_timeout_ms=self.kafka_config['consumer_timeout_ms']
            )
            self.conn = True
        except KafkaError:  # 发送失败抛出kafka_errors
            traceback.format_exc()
            self.conn = False
        # self.consumer.subscribe(self.kafka_config['topic'])

    def subscribe(self):
        """ 订阅主题列表或主题正则表达式模式。
        """
        if self.conn:
            self.consumer.subscribe(self.kafka_config['topic'])

    def topic_patterns(self)->set:
        """ 获取当前分配给此使用者的主题分区。

        Returns:
             set: {TopicPartition, ...}
        """
        return self.consumer.assignment()

    def consumer_from_offest(self, offest_id:any, patten:int =None, topic:str =None):
        """ 手动指定主题分区列表同时指定TopicPartition 的提取偏移量。

        Args:
            offest_id (any): offset偏移量
            patten (int, optional): 分区号. Defaults to None.
            topic (str, optional): 主题名称. Defaults to None.
        """
        pat = 0
        dstopic = self.kafka_config['topic']
        if patten:
            pat = patten
        if topic:
            dstopic = topic
        if self.conn:
            self.consumer.unsubscribe()
            self.consumer.assign([TopicPartition(dstopic, pat)])
            self.consumer.seek(TopicPartition(dstopic, pat), offest_id)

    @logger.catch
    def get_msgs_value_from_kafka(self)->list:
        """从卡夫卡读取一个消息队列，最大耗时0.2秒

        Returns:
            list: 消息队列，对多10条消息
        """
        try:
            value_list = []
            if not self.conn:
                self.init_consumer()
            if self.conn:
                curtime = tools.getcurtimestamp()
                for msg in self.consumer:
                    value_list.append(msg.value)
                    if tools.get_elapse_time(curtime) > 0.2 or len(value_list) >= 10:
                        break
                return value_list
            return None
        except Exception as err:
            logger.error(err)
            return None

    @logger.catch
    def get_one_msg_from_kafka(self)->dict:
        """从卡夫卡读取一条消息
        Returns:
            dict: 一条消息
        """
        try:
            if not self.conn:
                self.init_consumer()
            if self.conn:
                for msg in self.consumer:
                    return msg.value
                return None
            return None
        except Exception as err:
            logger.error(err)
            return None