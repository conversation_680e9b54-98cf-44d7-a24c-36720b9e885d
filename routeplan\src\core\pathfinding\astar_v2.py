# -*- coding: utf-8 -*-

import heapq
from os import path
from typing import List, Set, Dict, Tuple, Optional

import test
from ..node_3d import GridNode3D
from collections import OrderedDict
from ...config.settings import settings


class LRUCache:
    """具有大小限制的 LRU (Least Recently Used) 缓存实现
    用于存储经常访问的数据，当缓存达到容量上限时，自动删除最久未使用的项
    """

    def __init__(self, capacity: int):
        """初始化LRU缓存

        Args:
            capacity: 缓存的最大容量
        """
        self.cache = OrderedDict()  # 使用OrderedDict来维护项目的访问顺序
        self.capacity = capacity

    def get(self, key):
        """获取缓存中的值，如果存在则将其移到最近使用的位置

        Args:
            key: 要获取的键

        Returns:
            如果键存在则返回对应的值，否则返回None
        """
        if key not in self.cache:
            return None
        # 移动到最近使用的位置
        self.cache.move_to_end(key)
        return self.cache[key]

    def put(self, key, value):
        """将键值对添加到缓存中

        Args:
            key: 要添加的键
            value: 要添加的值
        """
        if key in self.cache:
            self.cache.move_to_end(key)
        self.cache[key] = value
        if len(self.cache) > self.capacity:
            self.cache.popitem(last=False)  # 删除最久未使用的项


class OptimizedPathFinder:
    """优化的3D路径规划器，使用跳点搜索（JPS）算法
    实现了垂直起飞、巡航和降落三个阶段的路径规划
    """

    def __init__(self, map3d):
        """初始化路径规划器

        Args:
            map3d: 3D地图对象，包含环境信息
        """
        self.map = map3d  # 3D地图实例
        self.height = map3d.height  # 地图高度
        self.width = map3d.width  # 地图宽度
        self.depth = map3d.depth  # 地图深度

        self.takeoff_speed_t = 1 / settings.pathplanning.takeoff_speed
        self.cruise_speed_t = 1 / settings.pathplanning.cruise_speed
        self.landing_speed_t = 1 / settings.pathplanning.landing_speed

        # 定义移动方向优先级（按重要性排序）
        self.DIRECTION_PRIORITIES = [
            # 第一优先级：XY平面基本方向
            (0, 1, 0),  # 右
            (0, -1, 0),  # 左
            (1, 0, 0),  # 前
            (-1, 0, 0),  # 后
            # 第二优先级：XY平面对角线（斜向移动）
            (1, 1, 0),  # 右前
            (1, -1, 0),  # 左前
            (-1, 1, 0),  # 右后
            (-1, -1, 0),  # 左后
            # 第三优先级：Z轴移动（高度变化，能耗较大）
            (0, 0, 1),  # 上升
            (0, 0, -1),  # 下降
        ]

        # 预计算强制邻居的检查方向
        # 强制邻居是指：在某个方向移动被阻挡时，需要检查的替代路径点
        self.FORCED_NEIGHBOR_DIRS = {
            # 基本方向移动时的检查点
            # 当向后移动(-1,0,0)时需要检查的点
            (-1, 0, 0): [
                ((0, 1, 0), (-1, 1, 0)),  # 检查右侧和右后
                ((0, -1, 0), (-1, -1, 0)),  # 检查左侧和左后
                ((0, 0, 1), (-1, 0, 1)),  # 检查上方和后上
                ((0, 0, -1), (-1, 0, -1)),  # 检查下方和后下
            ],
            # 当向前移动(1,0,0)时需要检查的点
            (1, 0, 0): [
                ((0, 1, 0), (1, 1, 0)),  # 检查右侧和右前
                ((0, -1, 0), (1, -1, 0)),  # 检查左侧和左前
                ((0, 0, 1), (1, 0, 1)),  # 检查上方和前上
                ((0, 0, -1), (1, 0, -1)),  # 检查下方和前下
            ],
            # 当向左移动(0,-1,0)时需要检查的点
            (0, -1, 0): [
                ((1, 0, 0), (1, -1, 0)),  # 检查前方和左前
                ((-1, 0, 0), (-1, -1, 0)),  # 检查后方和左后
                ((0, 0, 1), (0, -1, 1)),  # 检查上方和左上
                ((0, 0, -1), (0, -1, -1)),  # 检查下方和左下
            ],
            # 当向右移动(0,1,0)时需要检查的点
            (0, 1, 0): [
                ((1, 0, 0), (1, 1, 0)),  # 检查前方和右前
                ((-1, 0, 0), (-1, 1, 0)),  # 检查后方和右后
                ((0, 0, 1), (0, 1, 1)),  # 检查上方和右上
                ((0, 0, -1), (0, 1, -1)),  # 检查下方和右下
            ],
            # 当向下移动(0,0,-1)时需要检查的点
            (0, 0, -1): [
                ((1, 0, 0), (1, 0, -1)),  # 检查前方和前下
                ((-1, 0, 0), (-1, 0, -1)),  # 检查后方和后下
                ((0, 1, 0), (0, 1, -1)),  # 检查右侧和右下
                ((0, -1, 0), (0, -1, -1)),  # 检查左侧和左下
            ],
            # 当向上移动(0,0,1)时需要检查的点
            (0, 0, 1): [
                ((1, 0, 0), (1, 0, 1)),  # 检查前方和前上
                ((-1, 0, 0), (-1, 0, 1)),  # 检查后方和后上
                ((0, 1, 0), (0, 1, 1)),  # 检查右侧和右上
                ((0, -1, 0), (0, -1, 1)),  # 检查左侧和左上
            ],
            # 对角线移动时的检查点（包含三步检查路径）
            # 当向右前方移动(1,1,0)时需要检查的点
            (1, 1, 0): [
                # 垂直方向的检查
                ((0, 0, 1), (1, 1, 1)),  # 检查上方和右前上
                ((0, 0, -1), (1, 1, -1)),  # 检查下方和右前下
                # 如果前方(1,0,0)可走，检查经过(2,0,0)到达(2,1,0)的路径
                ((1, 0, 0), (2, 0, 0), (2, 1, 0)),
                # 如果右侧(0,1,0)可走，检查经过(0,2,0)到达(1,2,0)的路径
                ((0, 1, 0), (0, 2, 0), (1, 2, 0)),
            ],
            # 其他对角线移动方向的检查点类似，遵循相同的模式
            (1, -1, 0): [
                # 垂直方向的检查
                ((0, 0, 1), (1, -1, 1)),
                ((0, 0, -1), (1, -1, -1)),
                # 如果前方(1,0,0)可走，先到(2,0,0)再到(2,-1,0)
                ((1, 0, 0), (2, 0, 0), (2, -1, 0)),
                # 如果左侧(0,-1,0)可走，先到(0,-2,0)再到(1,-2,0)
                ((0, -1, 0), (0, -2, 0), (1, -2, 0)),
            ],
            # 右后对角线移动(-1,1,0)被阻塞时
            (-1, 1, 0): [
                # 垂直方向的检查
                ((0, 0, 1), (-1, 1, 1)),
                ((0, 0, -1), (-1, 1, -1)),
                # 如果后方(-1,0,0)可走，先到(-2,0,0)再到(-2,1,0)
                ((-1, 0, 0), (-2, 0, 0), (-2, 1, 0)),
                # 如果右侧(0,1,0)可走，先到(0,2,0)再到(-1,2,0)
                ((0, 1, 0), (0, 2, 0), (-1, 2, 0)),
            ],
            # 左后对角线移动(-1,-1,0)被阻塞时
            (-1, -1, 0): [
                # 垂直方向的检查
                ((0, 0, 1), (-1, -1, 1)),
                ((0, 0, -1), (-1, -1, -1)),
                # 如果后方(-1,0,0)可走，先到(-2,0,0)再到(-2,-1,0)
                ((-1, 0, 0), (-2, 0, 0), (-2, -1, 0)),
                # 如果左侧(0,-1,0)可走，先到(0,-2,0)再到(-1,-2,0)
                ((0, -1, 0), (0, -2, 0), (-1, -2, 0)),
            ],
        }

        # 初始化各种缓存，用于提高性能
        CACHE_SIZE = 10000  # 设置合理的缓存大小
        # self.neighbor_cache = LRUCache(CACHE_SIZE)  # 缓存邻居节点
        self.static_neighbor_cache = LRUCache(50000)  # 静态邻居缓存
        self.valid_position_cache = LRUCache(CACHE_SIZE)  # 缓存有效位置
        self.constraint_cache = LRUCache(CACHE_SIZE)  # 缓存约束检查结果
        self.distance_cache = LRUCache(CACHE_SIZE)  # 缓存距离计算结果

    def _check_constraints_and_collisions(
        self, pos, t, agent_id, constraints, occupancy_map
    ):
        """检查位置和时间是否满足约束条件和碰撞检测

        Args:
            pos: 位置坐标 (y, x, z)
            t: 时间戳，表示当前时刻
            agent_id: 智能体ID，用于区分不同无人机
            constraints: 约束列表，包含时空约束条件
            occupancy_map: 占用图对象，记录其他无人机的位置

        Returns:
            Tuple[bool, Optional[str]]: (是否可行, 错误原因)，如果可行返回(True, None)
        """
        # 首先检查地图中的静态障碍物类型
        if not self.map.traversable_map[pos[0], pos[1], pos[2]]:
            obstacle_types = self.map.obstacle_manager.get_type_at_position(pos)
            return False, f"位置 {pos} 与障碍物类型 {obstacle_types} 发生碰撞"

        # 检查与其他无人机的碰撞
        if occupancy_map:
            has_collision, colliding_agent = occupancy_map.check_collision(pos, t)
            if has_collision and colliding_agent != agent_id:
                return False, f"与无人机 {colliding_agent} 在时刻 {t} 发生碰撞"

        # 检查时空约束条件
        if constraints:
            for constraint in constraints:
                c_type, c_data = constraint
                if c_type == "v":  # 顶点约束（vertex constraint）
                    c_agent, c_y, c_x, c_z, c_t = c_data
                    if (
                        agent_id == c_agent
                        and pos[0] == c_y
                        and pos[1] == c_x
                        and pos[2] == c_z
                        and t == c_t
                    ):
                        return False, f"违反约束 {constraint}"

        return True, None

    def _is_valid_position(
        self,
        pos: Tuple[int, int, int],
        t: int,
        min_height: int,
        agent_id: str,
        constraints: Optional[List],
        occupancy_map,
        ignore_min_height: bool = False,
    ) -> Tuple[bool, Optional[str]]:
        """使用缓存优化的位置有效性检查

        Args:
            pos: 位置坐标 (y, x, z)
            t: 时间戳
            min_height: 最小飞行高度限制
            agent_id: 智能体ID
            constraints: 约束列表
            occupancy_map: 占用图对象
            ignore_min_height: 是否忽略最小高度限制，起飞阶段为True

        Returns:
            Tuple[bool, Optional[str]]: (位置是否有效, 错误原因)
        """
        if (
            pos[0] >= self.map.height
            or pos[1] >= self.map.width
            or pos[2] >= self.map.depth
            or pos[0] < 0
            or pos[1] < 0
            or pos[2] < 0
        ):
            return False, f"位置 {pos} 超出地图范围"

        # 快速路径：首先检查位置是否在预计算的可通行集合中
        if not self.map.traversable_map[pos[0], pos[1], pos[2]]:
            obstacle_types = self.map.obstacle_manager.get_type_at_position(pos)
            return False, f"位置 {pos} 与障碍物类型 {obstacle_types} 发生碰撞"

        # 检查最小高度约束（除非明确忽略）
        if not ignore_min_height and pos[2] < min_height:
            return False, f"飞行高度 {pos[2]} 低于最小巡航高度 {min_height}"

        # 检查约束和碰撞（使用缓存优化）
        if constraints or occupancy_map:
            # 使用位置和时间的哈希作为缓存键
            cache_key = hash((pos, t))
            cached_result = self.constraint_cache.get(cache_key)
            if cached_result is not None:
                return cached_result

            # 缓存未命中，执行实际检查
            is_valid, error = self._check_constraints_and_collisions(
                pos, t, agent_id, constraints, occupancy_map
            )
            # 保存结果到缓存
            self.constraint_cache.put(cache_key, (is_valid, error))
            return is_valid, error

        return True, None

    def _get_neighbors(
        self,
        node: GridNode3D,
        min_height: int,
        agent_id: str = None,
        constraints: Optional[List] = None,
        occupancy_map=None,
    ) -> List[Tuple[int, int, int]]:
        """获取节点的邻居位置，使用优先级顺序和缓存优化

        Args:
            node: 当前节点
            min_height: 最小飞行高度
            agent_id: 智能体ID
            constraints: 约束列表
            occupancy_map: 占用图对象

        Returns:
            List[Tuple[int, int, int]]: 可行邻居位置列表
        """
        # 静态邻居计算（固定障碍物）
        static_key = hash((node.y, node.x, node.z))
        static_neighbors = self.static_neighbor_cache.get(static_key)

        if static_neighbors is None:
            # 计算基础静态邻居（仅考虑固定障碍物）
            static_neighbors = []
            for dy, dx, dz in self.DIRECTION_PRIORITIES:
                ny = node.y + dy
                nx = node.x + dx
                nz = node.z + dz
                if (
                    ny >= self.map.height
                    or nx >= self.map.width
                    or nz >= self.map.depth
                    or ny < 0
                    or nx < 0
                    or nz < 0
                ):
                    continue

                if self.map.traversable_map[ny, nx, nz]:
                    static_neighbors.append((ny, nx, nz))
            self.static_neighbor_cache.put(static_key, static_neighbors)

        # 无动态因素时直接返回
        if not (constraints or occupancy_map):
            return static_neighbors

        valid_neighbors = []
        for pos in static_neighbors:
            # 执行实际动态检查
            is_valid, _ = self._is_valid_position(
                pos, node.t + 1, min_height, agent_id, constraints, occupancy_map
            )
            if is_valid:
                valid_neighbors.append(pos)

        return valid_neighbors

        # # 使用节点坐标的哈希值作为缓存键
        # cache_key = hash((node.y, node.x, node.z))
        # cached_result = self.neighbor_cache.get(cache_key)
        # if cached_result is not None:
        #     return cached_result

        # neighbors = []

        # # 按预定义的优先级顺序检查邻居位置
        # for dy, dx, dz in self.DIRECTION_PRIORITIES:
        #     ny = node.y + dy
        #     nx = node.x + dx
        #     nz = node.z + dz
        #     next_pos = (ny, nx, nz)

        #     # 快速检查：位置是否在可通行集合中
        #     if next_pos not in self.traversable_positions:
        #         continue

        #     # 详细检查位置的有效性
        #     if self._is_valid_position(
        #         next_pos, node.t + 1, min_height, agent_id, constraints, occupancy_map
        #     ):
        #         neighbors.append(next_pos)

        # # 将结果保存到缓存
        # self.neighbor_cache.put(cache_key, neighbors)
        # return neighbors

    def _manhattan_distance(
        self, pos1: Tuple[int, int, int], pos2: Tuple[int, int, int]
    ) -> int:
        """计算曼哈顿距离，用作启发式估计值

        Args:
            pos1, pos2: 两个位置坐标 (y, x, z)

        Returns:
            int: 两点间的曼哈顿距离
        """
        return abs(pos2[0] - pos1[0]) + abs(pos2[1] - pos1[1]) + abs(pos2[2] - pos1[2])

    def _euclidean_distance(
        self, pos1: Tuple[int, int, int], pos2: Tuple[int, int, int]
    ) -> float:
        """计算欧几里得距离，用作启发式估计值"""
        return (
            (pos2[0] - pos1[0]) ** 2
            + (pos2[1] - pos1[1]) ** 2
            + (pos2[2] - pos1[2]) ** 2
        )

    def _has_forced_neighbor(
        self,
        current_node,
        direction,
        min_height,
        agent_id=None,
        constraints=None,
        occupancy_map=None,
    ):
        """检查在给定方向上是否存在强制邻居

        强制邻居是指：当某个移动方向被阻挡时，为保证路径完整性必须检查的替代路径点。
        这些点在跳点搜索中起着关键作用，可能会引导搜索到更优的路径。

        Args:
            current_node: 当前节点
            direction: 移动方向
            min_height: 最小飞行高度
            agent_id: 智能体ID
            constraints: 约束列表
            occupancy_map: 占用图对象

        Returns:
            bool: 是否存在强制邻居
        """
        current_pos = (current_node.y, current_node.x, current_node.z)

        # # 检查主方向是否被阻塞
        # next_pos = (
        #     current_pos[0] + direction[0],
        #     current_pos[1] + direction[1],
        #     current_pos[2] + direction[2],
        # )

        # if self._is_valid_position(
        #     next_pos,
        #     current_node.t + 1,
        #     min_height,
        #     agent_id,
        #     constraints,
        #     occupancy_map,
        # ):
        #     return False

        # 如果当前方向没有预定义的强制邻居检查模式，直接返回False
        if direction not in self.FORCED_NEIGHBOR_DIRS:
            return False

        # 检查每个可能的强制邻居路径
        for path in self.FORCED_NEIGHBOR_DIRS[direction]:
            # 快速检查路径的最终点
            last_point = path[-1]
            last_pos = (
                current_pos[0] + last_point[0],
                current_pos[1] + last_point[1],
                current_pos[2] + last_point[2],
            )

            # 如果终点不可通行，跳过此路径, 删除，可能会超过地图范围
            # if not self.map.traversable_map[last_pos[0], last_pos[1], last_pos[2]]:
            #     continue

            # 检查路径上的所有点是否都可通行
            all_valid = True
            for i, point in enumerate(path):
                check_pos = (
                    current_pos[0] + point[0],
                    current_pos[1] + point[1],
                    current_pos[2] + point[2],
                )

                is_valid, _ = self._is_valid_position(
                    check_pos,
                    current_node.t + i + 1,
                    min_height,
                    agent_id,
                    constraints,
                    occupancy_map,
                )
                if not is_valid:
                    all_valid = False
                    break

            # 如果找到一条完全可行的强制邻居路径，返回True
            if all_valid:
                return True

        return False

    def _jump(
        self,
        current: GridNode3D,
        parent: Optional[GridNode3D],
        goal: Tuple[int, int, int],
        min_height: int,
        agent_id: str,
        constraints: Optional[List],
        occupancy_map,
    ) -> Optional[Tuple[GridNode3D, List[GridNode3D]]]:
        """跳点搜索的核心函数，沿着一个方向不断前进直到遇到跳点

        跳点的条件：
        1. 到达目标点
        2. 遇到障碍物
        3. 发现强制邻居
        4. 到达地图边界

        Args:
            current: 当前节点
            parent: 父节点（用于确定搜索方向）
            goal: 目标位置
            min_height: 最小飞行高度
            agent_id: 智能体ID
            constraints: 约束列表
            occupancy_map: 占用图对象

        Returns:
            Optional[Tuple[GridNode3D, List[GridNode3D]]]:
            如果找到跳点，返回(跳点节点, 从当前节点到跳点的路径)
            如果未找到跳点，返回None
        """
        if not current:
            return None

        current_pos = (current.y, current.x, current.z)

        # 快速目标检查
        if current_pos == goal:
            current.parent = parent
            return current, [current]

        # 快速有效性检查
        # if current_pos not in self.traversable_positions or current.z < min_height:
        #     return None

        # 如果是起始节点，直接返回
        if not parent:
            return current, [current]

        # 计算移动方向
        dy = current.y - parent.y
        dx = current.x - parent.x
        dz = current.z - parent.z
        direction = (dy, dx, dz)

        # 初始化路径
        intermediate_path = [current]
        current.parent = parent

        # 沿着同一方向继续移动
        next_y = current.y + dy
        next_x = current.x + dx
        next_z = current.z + dz
        current_t = current.t

        while True:
            next_pos = (next_y, next_x, next_z)

            # 快速检查下一个位置
            # if next_pos not in self.traversable_positions:
            #     break

            # 检查是否可达
            is_valid, error = self._is_valid_position(
                next_pos,
                current_t + self.cruise_speed_t,
                min_height,
                agent_id,
                constraints,
                occupancy_map,
            )
            if not is_valid:
                break

            # 创建下一个节点
            next_node = GridNode3D(
                next_pos[0], next_pos[1], next_pos[2], current_t + self.cruise_speed_t
            )
            next_node.parent = intermediate_path[-1]
            intermediate_path.append(next_node)

            # 检查是否到达目标
            if next_pos == goal:
                return next_node, intermediate_path

            # 检查是否有强制邻居
            if self._has_forced_neighbor(
                next_node,
                direction,
                min_height,
                agent_id,
                constraints,
                occupancy_map,
            ):
                return next_node, intermediate_path

            # 更新位置和时间
            next_y += dy
            next_x += dx
            next_z += dz
            current_t += self.cruise_speed_t

            # 检查是否在远离目标（避免无效搜索）
            # if abs(goal[0] - next_y) + abs(goal[1] - next_x) + abs(
            #     goal[2] - next_z
            # ) >= abs(goal[0] - current_pos[0]) + abs(goal[1] - current_pos[1]) + abs(
            #     goal[2] - current_pos[2]
            # ):
            #     break
            # 检查是否还在向目标靠近
            # if self._manhattan_distance(
            #     (next_y, next_x, next_z), goal
            # ) >= self._manhattan_distance(next_pos, goal):
            #     break
            if self._euclidean_distance(
                (next_y, next_x, next_z), goal
            ) >= self._euclidean_distance(next_pos, goal):
                break

        return intermediate_path[-1], intermediate_path

    def euclidean_distance_squared(
        self, pos1: Tuple[int, int, int], pos2: Tuple[int, int, int]
    ) -> float:
        """计算两点间的欧几里得距离的平方（避免开方运算提高性能）

        Args:
            pos1: 第一个点的坐标 (y, x, z)
            pos2: 第二个点的坐标 (y, x, z)

        Returns:
            float: 欧几里得距离的平方
        """
        return (
            (pos1[0] - pos2[0]) ** 2
            + (pos1[1] - pos2[1]) ** 2
            + (pos1[2] - pos2[2]) ** 2
        )

    def has_line_of_sight(
        self,
        pos1: Tuple[int, int, int],
        pos2: Tuple[int, int, int],
        t: Optional[int] = None,
        min_height: int = 0,
        agent_id: str = "0",
        constraints: Optional[List] = None,
        occupancy_map=None,
    ) -> Tuple[bool, List[Tuple[Tuple[int, int, int], int]]]:
        """检查两点之间是否有直接视线（考虑时间约束和占用图）

        Args:
            pos1: 起始位置 (y, x, z)
            pos2: 目标位置 (y, x, z)
            t: 当前时间戳（如果指定）
            min_height: 最小巡航高度
            agent_id: 智能体ID
            constraints: 时空约束列表
            occupancy_map: 占用图

        Returns:
            Tuple[bool, List[Tuple[Tuple[int, int, int], int]]]:
            - bool: 如果两点之间有直接视线且满足约束条件返回True，否则返回False
            - List: 路径上的所有点及其对应的时间戳列表，格式为[(pos, t), ...]
        """
        # 如果点过于接近，不需要视线检查
        # if self.euclidean_distance_squared(pos1, pos2) < 25:
        #     return True, [(pos1, t if t is not None else 0)]

        # 计算方向向量和步数
        dy = pos2[0] - pos1[0]
        dx = pos2[1] - pos1[1]
        dz = pos2[2] - pos1[2]
        steps = max(abs(dy), abs(dx), abs(dz))

        if steps == 0:
            return True, [(pos1, t if t is not None else 0)]

        # 计算每步的增量
        sy = dy / steps
        sx = dx / steps
        sz = dz / steps

        # 检查路径上的每个点
        current_t = t if t is not None else 0
        path_points = []  # 存储路径上的点和时间戳
        path_points.append(GridNode3D(pos1[0], pos1[1], pos1[2], current_t))
        last_pos = None  # 记录上一个点的位置

        for i in range(steps + 1):
            # 计算当前检查点的坐标（四舍五入到最近的网格点）
            y_float = pos1[0] + sy * i
            x_float = pos1[1] + sx * i
            z_float = pos1[2] + sz * i
            y = int(y_float + 0.5)
            x = int(x_float + 0.5)
            z = int(z_float + 0.5)
            # y = int(pos1[0] + sy * i + 0.5)
            # x = int(pos1[1] + sx * i + 0.5)
            # z = int(pos1[2] + sz * i + 0.5)
            current_pos = (y, x, z)

            # 如果当前点与上一个点相同，跳过这个点但保持时间不变
            if last_pos == current_pos:
                continue

            current_t += self.cruise_speed_t  # 只有在点不重复时才增加时间

            # 检查位置是否有效（包括约束和占用情况）
            is_valid, error = self._is_valid_position(
                current_pos,
                current_t,  # 使用当前时间
                min_height,
                agent_id,
                constraints,
                occupancy_map,
            )
            if not is_valid:
                return False, [f"路径点 {current_pos} 在时刻 {current_t} {error}"]

            # 记录有效点及其时间戳
            # path_points.append(GridNode3D(y, x, z, current_t))
            path_points.append(GridNode3D(y_float, x_float, z_float, current_t))
            last_pos = current_pos  # 更新上一个点的位置

        path_points.append(GridNode3D(pos2[0], pos2[1], pos2[2], current_t))
        return True, path_points

    def _find_cruise_path(
        self,
        start: Tuple[int, int, int],
        goal: Tuple[int, int, int],
        min_height: int,
        agent_id: str,
        start_time: Optional[int],
        occupancy_map,
        constraints: Optional[List] = None,
    ) -> Tuple[Optional[List[GridNode3D]], Optional[str]]:
        """使用优化的跳点搜索进行巡航路径规划

        在固定高度层执行路径搜索，这是整个路径规划中最复杂的阶段。
        使用A*算法的框架，但结合了跳点搜索(JPS)来优化搜索效率。

        Args:
            start: 起始位置坐标 (y, x, z)
            goal: 目标位置坐标 (y, x, z)
            min_height: 最小巡航高度
            agent_id: 智能体ID
            start_time: 开始时间（如果指定）
            occupancy_map: 占用图对象
            constraints: 时空约束列表

        Returns:
            Tuple[Optional[List[GridNode3D]], Optional[str]]:
            - 如果成功，返回(路径节点列表, None)
            - 如果失败，返回(None, 错误信息)
        """
        # 首先检查起点和终点之间是否有直接视线
        has_los, los_points = self.has_line_of_sight(
            start, goal, start_time, min_height, agent_id, constraints, occupancy_map
        )

        if has_los:
            return los_points, None, True

        open_list = []  # 优先队列，存储待探索的节点
        closed = set()  # 已探索的节点集合
        counter = 0  # 用于打破f值相等时的平局
        path_nodes = {}  # 存储路径节点，避免重复创建
        # intermediate_paths = {}  # 存储节点间的完整中间路径

        def heuristic(pos: Tuple[int, int, int]) -> float:
            """启发式函数：使用曼哈顿距离"""
            return self._manhattan_distance(pos, goal)
            # return self._euclidean_distance(pos, goal)

        # 初始化起始节点
        # t = start_time if start_time is not None else 0
        start_node = GridNode3D(start[0], start[1], start[2], start_time)
        # start = (start_node.y, start_node.x, start_node.z)
        start_node.g = 0
        start_node.h = heuristic(start)
        start_node.f = start_node.h
        # start_node.parent = None

        # 将起始节点加入开启列表
        heapq.heappush(open_list, (start_node.f, counter, start_node))
        path_nodes[start] = start_node

        # 搜索主循环
        while open_list:
            # 取出f值最小的节点
            current = heapq.heappop(open_list)[2]
            current_pos = (current.y, current.x, current.z)

            # 检查是否到达目标
            if current_pos == goal:
                # 重建完整路径
                path = []
                node = current
                while node:
                    path.append(node)
                    node = node.parent
                return path[::-1], None, False

            # 如果节点已经探索过，跳过
            if current_pos in closed:
                continue

            # 将当前节点加入已探索集合
            closed.add(current_pos)

            # 获取并处理邻居节点
            for next_pos in self._get_neighbors(
                current,
                min_height,
                agent_id,
                constraints,
                occupancy_map,
            ):
                # 如果邻居节点已经探索过，跳过
                if next_pos in closed:
                    continue

                # 创建临时节点用于跳点搜索
                next_t = current.t + self.cruise_speed_t
                temp_node = GridNode3D(next_pos[0], next_pos[1], next_pos[2], next_t)
                temp_node.parent = current

                # 尝试跳点搜索，获取跳点和中间路径
                jump_result = self._jump(
                    temp_node,
                    current,
                    goal,
                    min_height,
                    agent_id,
                    constraints,
                    occupancy_map,
                )

                if not jump_result:
                    continue

                jump_node, intermediate_path = jump_result

                # 计算从起点经过当前节点到跳点的真实代价
                new_g = current.g + len(intermediate_path)

                # 检查是否找到更好的路径
                jump_pos = (jump_node.y, jump_node.x, jump_node.z)
                if jump_pos in path_nodes:
                    existing_node = path_nodes[jump_pos]
                    if new_g >= existing_node.g:
                        continue

                # 更新跳点节点的路径信息
                jump_node.g = new_g
                jump_node.h = heuristic(jump_pos)
                jump_node.f = jump_node.g + jump_node.h
                # jump_node.parent = current

                # 保存中间路径，并确保路径中所有节点的父节点关系正确
                # for i in range(1, len(intermediate_path)):
                #     intermediate_path[i].parent = intermediate_path[i - 1]
                # intermediate_paths[(jump_node, current_pos)] = intermediate_path

                # 更新路径节点字典
                path_nodes[jump_pos] = jump_node

                # 将跳点加入开启列表
                counter += self.cruise_speed_t
                heapq.heappush(open_list, (jump_node.f, counter, jump_node))

        return None, "巡航阶段无法找到路径", None

    def _vertical_takeoff(
        self,
        start: Tuple[int, int, int],
        min_height: int,
        agent_id: str,
        start_time: Optional[int],
        occupancy_map,
        constraints: Optional[List] = None,
    ) -> Tuple[Optional[List[GridNode3D]], Optional[str]]:
        """执行垂直起飞阶段的路径规划"""
        path = []
        current_t = start_time if start_time is not None else 0

        # 从起点垂直上升到最小巡航高度
        for z in range(start[2], min_height + 1):
            pos = (start[0], start[1], z)

            # 检查约束和碰撞，在起飞阶段忽略最小高度限制
            is_valid, error = self._is_valid_position(
                pos,
                current_t,
                min_height,
                agent_id,
                constraints,
                occupancy_map,
                ignore_min_height=True,
            )
            if not is_valid:
                real_z = self.map.grid_converter.relative_to_geo(0, 0, z)["alt"]
                return None, f"垂直爬升阶段在高度 {real_z} {error}"

            node = GridNode3D(start[0], start[1], z, current_t)
            path.append(node)
            current_t += self.takeoff_speed_t

        return path, None

    def _vertical_landing(
        self,
        last_cruise_node: GridNode3D,
        goal: Tuple[int, int, int],
        agent_id: str,
        occupancy_map,
        constraints: Optional[List] = None,
    ) -> Tuple[Optional[List[GridNode3D]], Optional[str]]:
        """执行垂直降落阶段的路径规划"""
        path = []
        current_t = last_cruise_node.t + self.landing_speed_t

        # 从巡航高度垂直下降到目标点
        for z in range(last_cruise_node.z - 1, goal[2] - 1, -1):
            pos = (goal[0], goal[1], z)

            # 检查约束和碰撞，降落阶段不需要考虑最小高度
            is_valid, error = self._is_valid_position(
                pos,
                current_t,
                0,  # 降落阶段不需要考虑最小高度限制
                agent_id,
                constraints,
                occupancy_map,
            )
            if not is_valid:
                return None, f"降落阶段在高度 {z} {error}"

            node = GridNode3D(goal[0], goal[1], z, current_t)
            path.append(node)
            current_t += self.landing_speed_t

        return path, None

    def find_path(
        self,
        start: Tuple[int, int, int],
        goal: Tuple[int, int, int],
        min_height: int = 0,
        agent_id: str = "0",
        start_time: Optional[int] = None,
        occupancy_map=None,
        constraints: Optional[List] = None,
    ) -> Tuple[Optional[List[GridNode3D]], Optional[str]]:
        """主路径规划方法，将路径规划分为三个阶段

        完整的路径规划包括三个阶段：
        1. 垂直起飞：从起点上升到最小巡航高度
        2. 巡航：在固定高度层执行路径搜索
        3. 垂直降落：从巡航高度下降到目标点

        Args:
            start: 起点坐标 (y, x, z)
            goal: 终点坐标 (y, x, z)
            min_height: 最小巡航高度，默认为0
            agent_id: 智能体标识，默认为"0"
            start_time: 起始时间（如果指定），默认为None
            occupancy_map: 用于多机避碰的占用图
            constraints: 时空约束列表，用于多机协调

        Returns:
            Tuple[Optional[List[GridNode3D]], Optional[str]]:
            - 如果成功，返回(完整路径节点列表, None)
            - 如果失败，返回(None, 错误信息)
        """
        # 1. 检查是否需要垂直起飞
        if start[2] >= min_height:
            # 起始高度已经满足最小高度要求，直接从当前高度开始巡航
            start_node = GridNode3D(
                start[0],
                start[1],
                start[2],
                start_time if start_time is not None else 0,
            )
            takeoff_path = [start_node]
        else:
            # 需要垂直起飞
            takeoff_path, error = self._vertical_takeoff(
                start, min_height, agent_id, start_time, occupancy_map, constraints
            )
            if not takeoff_path:
                return None, f"起飞阶段失败: {error}"

        # 2. 巡航阶段
        cruise_goal = (goal[0], goal[1], min_height)  # 在巡航高度的投影点
        cruise_path, error, is_linesight = self._find_cruise_path(
            (takeoff_path[-1].y, takeoff_path[-1].x, takeoff_path[-1].z),
            cruise_goal,
            min_height,
            agent_id,
            takeoff_path[-1].t,  # 使用起飞结束时刻作为巡航开始时间
            occupancy_map,
            constraints,
        )
        if not cruise_path:
            return None, f"巡航阶段失败: {error}"

        # 确保巡航路径与起飞路径正确连接
        # if cruise_path and takeoff_path:
        #     cruise_path[0].parent = takeoff_path[-1]

        # 3. 垂直降落阶段
        landing_path, error = self._vertical_landing(
            cruise_path[-1], goal, agent_id, occupancy_map, constraints
        )
        if not landing_path:
            return None, f"降落阶段失败: {error}"

        if is_linesight:
            cruise_turn_path = cruise_path[:1] + cruise_path[-1:]
        else:
            cruise_turn_path = self.extract_turning_points(cruise_path)
        complete_turn_path = takeoff_path[:1] + cruise_turn_path + landing_path[-1:]
        # else:
        # 合并三个阶段的路径，形成完整路径
        # 注意：去掉重复的连接点（起飞终点和巡航起点）
        complete_path = takeoff_path[:-1] + cruise_path + landing_path

        return (complete_path, complete_turn_path), None

    def extract_turning_points(self, path: List[GridNode3D]) -> List[GridNode3D]:
        """从完整路径中提取拐点（方向变化的点）

        分析路径中的每个点，只保留起点、终点和方向发生变化的点，
        从而得到一个简化的路径，只包含关键的拐点。

        Args:
            path: 完整的路径点列表

        Returns:
            List[GridNode3D]: 只包含拐点的路径列表
        """
        if not path or len(path) <= 2:
            return path  # 如果路径为空或只有两个点，直接返回原路径

        turning_points = [path[0]]  # 始终包含起点

        # 遍历路径中的点，检测方向变化
        for i in range(1, len(path) - 1):
            prev_node = path[i - 1]
            curr_node = path[i]
            next_node = path[i + 1]

            # 计算当前段和下一段的方向向量
            curr_dir = (
                curr_node.y - prev_node.y,
                curr_node.x - prev_node.x,
                curr_node.z - prev_node.z,
            )
            next_dir = (
                next_node.y - curr_node.y,
                next_node.x - curr_node.x,
                next_node.z - curr_node.z,
            )

            # 如果方向发生变化，则当前点是拐点
            if curr_dir != next_dir:
                turning_points.append(curr_node)

        turning_points.append(path[-1])  # 始终包含终点

        return turning_points
